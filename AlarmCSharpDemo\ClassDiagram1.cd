﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="CopyDataStruct.AlarmDemo" Collapsed="true">
    <Position X="0.5" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>cCAAAABBACAAABAAAADDAACyBCAIAAAEAzAAAAJgAEA=</HashCode>
    </TypeIdentifier>
  </Class>
  <Class Name="AlarmCSharpDemo.AlarmDemo" Collapsed="true">
    <Position X="2.25" Y="0.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>MCAkEAIEEBJEFBAEAmghSQAAwIaIiEgIBMEYCEjgAyA=</HashCode>
      <FileName>AlarmDemo.cs</FileName>
    </TypeIdentifier>
  </Class>
  <Struct Name="CopyDataStruct.COPYDATASTRUCT" Collapsed="true">
    <Position X="0.5" Y="1.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAIAEAAAAAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAA=</HashCode>
      <FileName>AlarmDemo.cs</FileName>
    </TypeIdentifier>
  </Struct>
  <Font Name="宋体" Size="9" />
</ClassDiagram>