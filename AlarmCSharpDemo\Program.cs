﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using System.Threading;
using System.Diagnostics;

namespace AlarmCSharpDemo
{
    static class Program
    {
        // 定义一个静态Mutex变量
        private static Mutex mutex = null;
        
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            // 设置应用程序名称作为Mutex名称
            string mutexName = "AlarmCSharpDemo_SingleInstanceMutex";
            bool createdNew;
            
            // 尝试创建命名的Mutex
            mutex = new Mutex(true, mutexName, out createdNew);
            
            // 检查是否已经有一个实例在运行
            if (!createdNew)
            {
                // 已经有一个实例在运行
                MessageBox.Show("程序已经在运行中，不允许重复运行！", "提示", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                
                // 尝试激活已运行的实例
                BringExistingInstanceToFront();
                
                // 立即退出当前实例
                return;
            }
            
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new AlarmDemo());
            }
            finally
            {
                // 确保在应用程序结束时释放Mutex
                if (mutex != null)
                {
                    mutex.ReleaseMutex();
                    mutex.Close();
                }
            }
        }
        
        /// <summary>
        /// 尝试将已经运行的实例窗口激活到前台
        /// </summary>
        private static void BringExistingInstanceToFront()
        {
            try
            {
                // 查找与当前进程名称相同的所有进程
                Process current = Process.GetCurrentProcess();
                foreach (Process process in Process.GetProcessesByName(current.ProcessName))
                {
                    // 跳过当前进程
                    if (process.Id != current.Id)
                    {
                        // 激活找到的窗口
                        NativeMethods.SetForegroundWindow(process.MainWindowHandle);
                        break;
                    }
                }
            }
            catch (Exception)
            {
                // 忽略任何异常
            }
        }
    }
    
    /// <summary>
    /// 用于调用Windows API的类
    /// </summary>
    internal static class NativeMethods
    {
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        [return: System.Runtime.InteropServices.MarshalAs(System.Runtime.InteropServices.UnmanagedType.Bool)]
        internal static extern bool SetForegroundWindow(IntPtr hWnd);
    }
}
