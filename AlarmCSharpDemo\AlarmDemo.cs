using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing; // 引用 System.Drawing 命名空间
using System.Text;
using System.Windows.Forms;
using System.Runtime.InteropServices;
using System.Diagnostics;
using System.IO;
using CopyDataStruct;
using System.Text.RegularExpressions;
using System.Linq; // 添加LINQ支持

namespace AlarmCSharpDemo
{
    public partial class AlarmDemo : Form
    {

        [DllImport("User32.dll", EntryPoint = "SendMessage")]
        private static extern int SendMessage(int hWnd, int Msg, int wParam, ref COPYDATASTRUCT lParam);

        [DllImport("User32.dll", EntryPoint = "FindWindow")]
        private static extern int FindWindow(string lpClassName, string lpWindowName);

        const int WM_COPYDATA = 0x004A;
            
        private Int32 m_lUserID = -1;
        private Int32[] m_lAlarmHandle = new Int32[200];
        private Int32 iListenHandle = -1;
        private int iDeviceNumber = 0; //添加设备个数
        private int iFileNumber = 0; //保存的文件个数
        private uint iLastErr = 0;
        private string strErr;
        private bool isExitButtonClicked = false; // 添加标志位，标识是否通过退出按钮关闭

        private bool enableErrorLogging = false; // 是否启用错误日志记录
        private bool enablePictureSaving = false; // 是否启用图片保存功能

        // 添加标志位，标识是否已经进行过资源清理
        private static bool alreadyCleaned = false;

        public string currentTimeString;

        public CHCNetSDK.LOGINRESULTCALLBACK LoginCallBack = null;
        //private CHCNetSDK.EXCEPYIONCALLBACK m_fExceptionCB = null;
        private CHCNetSDK.MSGCallBack_V31 m_falarmData_V31 = null;
        private CHCNetSDK.MSGCallBack m_falarmData = null;

        public delegate void UpdateTextStatusCallback(string strLogStatus, IntPtr lpDeviceInfo);
        public delegate void UpdateListBoxCallback(string strAlarmTime, string strDevIP, string strAlarmMsg);
        public delegate void UpdateListBoxCallbackException(string strAlarmTime, int lUserID, string strAlarmMsg);

        CHCNetSDK.NET_VCA_TRAVERSE_PLANE m_struTraversePlane = new CHCNetSDK.NET_VCA_TRAVERSE_PLANE();
        CHCNetSDK.NET_VCA_AREA m_struVcaArea = new CHCNetSDK.NET_VCA_AREA();
        CHCNetSDK.NET_VCA_INTRUSION m_struIntrusion = new CHCNetSDK.NET_VCA_INTRUSION();
        CHCNetSDK.UNION_STATFRAME m_struStatFrame = new CHCNetSDK.UNION_STATFRAME();
        CHCNetSDK.UNION_STATTIME m_struStatTime = new CHCNetSDK.UNION_STATTIME();

        // 在类的顶部添加
        private ContextMenuStrip contextMenuStrip;
        private ToolStripMenuItem sendMessageMenuItem;

        // 使用静态变量跟踪整个应用程序的关闭状态
        private static bool isClosing = false;

        // 添加一个字段来存储当前照片的原始路径
        private string currentPhotoPath = string.Empty;

        public AlarmDemo()
        {
            InitializeComponent();

            // 确保picture目录存在
            if (!Directory.Exists(".\\picture"))
            {
                Directory.CreateDirectory(".\\picture");
            }

            // 注册标签页切换事件
            this.mainTabControl.SelectedIndexChanged += new EventHandler(mainTabControl_SelectedIndexChanged);
            
            // 重要：移除原有的窗体关闭事件注册，确保只有一个处理程序
            this.FormClosing -= new FormClosingEventHandler(AlarmDemo_FormClosing);
            // 注册我们的窗体关闭事件
            this.FormClosing += new FormClosingEventHandler(AlarmDemo_FormClosing);
            
            m_falarmData_V31 = new CHCNetSDK.MSGCallBack_V31(MsgCallback_V31);
            //m_fExceptionCB = new CHCNetSDK.EXCEPYIONCALLBACK(cbExceptionCB);
            if (m_falarmData_V31 == null)
            {
                MessageBox.Show("Callback function Initialize failed");
                return;
            }
            bool m_bInitSDK = CHCNetSDK.NET_DVR_Init();
            if (m_bInitSDK == false)
            {
                MessageBox.Show("NET_DVR_Init error!");
                return;
            }
            else
            {
                byte[] strIP = new byte[16 * 16];
                uint dwValidNum = 0;
                Boolean bEnableBind = false;

                //获取本地PC网卡IP信息
                if (CHCNetSDK.NET_DVR_GetLocalIP(strIP, ref dwValidNum, ref bEnableBind))
                {
                    if (dwValidNum > 0)
                    {
                        //取第一张网卡的IP地址为默认监听端口
                        textBoxListenIP.Text = System.Text.Encoding.UTF8.GetString(strIP, 0, 16);
                        CHCNetSDK.NET_DVR_SetValidIP(0,true); //绑定第一张网卡
                    }

                }

                //保存SDK日志 To save the SDK log
               // CHCNetSDK.NET_DVR_SetLogToFile(3, "C:\\SdkLog\\", true);

                //设置透传报警信息类型
                CHCNetSDK.NET_DVR_LOCAL_GENERAL_CFG struLocalCfg = new CHCNetSDK.NET_DVR_LOCAL_GENERAL_CFG();
                struLocalCfg.byAlarmJsonPictureSeparate = 1;//控制JSON透传报警数据和图片是否分离，0-不分离(COMM_VCA_ALARM返回)，1-分离（分离后走COMM_ISAPI_ALARM回调返回）

                Int32 nSize = Marshal.SizeOf(struLocalCfg);
                // MessageBox.Show(nSize.ToString());
                IntPtr ptrLocalCfg = Marshal.AllocHGlobal(nSize);
                Marshal.StructureToPtr(struLocalCfg, ptrLocalCfg, false);

                if (!CHCNetSDK.NET_DVR_SetSDKLocalCfg(17, ptrLocalCfg))  //NET_DVR_LOCAL_CFG_TYPE_GENERAL
                {
                    iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                    strErr = "NET_DVR_SetSDKLocalCfg failed, error code= " + iLastErr;
                    MessageBox.Show(strErr);
                }
                Marshal.FreeHGlobal(ptrLocalCfg);

                for (int i = 0; i < 200; i++)
                {
                    m_lAlarmHandle[i] = -1;
                }

                //设置异常消息回调函数
               // if (m_fExceptionCB == null)
               // {
               //     m_fExceptionCB = new CHCNetSDK.EXCEPYIONCALLBACK(cbExceptionCB);
              //  }
              //  CHCNetSDK.NET_DVR_SetExceptionCallBack_V30(0, IntPtr.Zero, m_fExceptionCB, IntPtr.Zero);


                //设置报警回调函数
                CHCNetSDK.NET_DVR_SetDVRMessageCallBack_V31(m_falarmData_V31, IntPtr.Zero);

                // 清空IP输入框
                textBoxIP.Text = "";
                
                //============================================================================================================================启动的时候添加两个IP并布防
                // 定义要添加的两个设备IP
                string[] deviceIPs = { "***********", "***********" };
                
                // 设置登录参数
                CHCNetSDK.NET_DVR_USER_LOGIN_INFO struLogInfo = new CHCNetSDK.NET_DVR_USER_LOGIN_INFO();
                
                // 设备用户名
                byte[] byUserName = System.Text.Encoding.Default.GetBytes(textBoxUserName.Text);
                struLogInfo.sUserName = new byte[64];
                byUserName.CopyTo(struLogInfo.sUserName, 0);

                // 设备密码
                byte[] byPassword = System.Text.Encoding.Default.GetBytes(textBoxPassword.Text);
                struLogInfo.sPassword = new byte[64];
                byPassword.CopyTo(struLogInfo.sPassword, 0);

                struLogInfo.wPort = ushort.Parse(textBoxPort.Text);//设备服务端口号
                struLogInfo.cbLoginResult = LoginCallBack;
                struLogInfo.bUseAsynLogin = false; //是否异步登录：0- 否，1- 是 

                if ((struLogInfo.bUseAsynLogin == true) && (LoginCallBack == null))
                {
                    LoginCallBack = new CHCNetSDK.LOGINRESULTCALLBACK(cbLoginCallBack);//注册回调函数                    
                }

                struLogInfo.byLoginMode = 0; //0-Private, 1-ISAPI, 2-自适应
                struLogInfo.byHttps = 0; //0-不适用tls，1-使用tls 2-自适应
                
                CHCNetSDK.NET_DVR_DEVICEINFO_V40 DeviceInfo = new CHCNetSDK.NET_DVR_DEVICEINFO_V40();

                // 循环添加两个设备
                foreach (string deviceIP in deviceIPs)
                {
                    // 检查IP是否已存在且布防成功（这里不需要检查布防状态，因为刚启动时没有布防成功的设备）
                    bool ipExists = false;
                    for (int i = 0; i < iDeviceNumber; i++)
                    {
                        string existingIP = listViewDevice.Items[i].SubItems[1].Text;
                        if (existingIP == deviceIP)
                        {
                            ipExists = true;
                            break;
                        }
                    }
                    
                    if (ipExists)
                    {
                        continue; // 跳过已存在的IP
                    }
                    
                    // 设备IP地址或者域名
                    byte[] byIP = System.Text.Encoding.Default.GetBytes(deviceIP);
                    struLogInfo.sDeviceAddress = new byte[129];
                    byIP.CopyTo(struLogInfo.sDeviceAddress, 0);
                    
                    // 登录设备
                    m_lUserID = CHCNetSDK.NET_DVR_Login_V40(ref struLogInfo, ref DeviceInfo);
                    if (m_lUserID < 0)
                    {
                        iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                        strErr = "NET_DVR_Login_V30 failed, error code= " + iLastErr; //登录失败，输出错误号
                        //MessageBox.Show(strErr); //注销错误提示，避免程序卡住
                    }
                    else
                    {
                        //登录成功
                        iDeviceNumber++;
                        string str1 = "" + m_lUserID;
                        
                        // 根据IP地址添加备注
                        string remark = "";
                        if (deviceIP == "***********")
                        {
                            remark = "小学";
                        }
                        else if (deviceIP == "***********")
                        {
                            remark = "初中";
                        }
                        
                        listViewDevice.Items.Add(new ListViewItem(new string[] { str1, deviceIP, "未布防", remark }));//将已注册设备添加进列表
                        
                        // 更新IP输入框为最后添加的IP
                        textBoxIP.Text = deviceIP;
                    }
                }
                
                //============================================================================================================================凌-全部布防
                CHCNetSDK.NET_DVR_SETUPALARM_PARAM struAlarmParam = new CHCNetSDK.NET_DVR_SETUPALARM_PARAM();
                struAlarmParam.dwSize = (uint)Marshal.SizeOf(struAlarmParam);
                struAlarmParam.byLevel = 1; //0- 一级布防,1- 二级布防
                struAlarmParam.byAlarmInfoType = 1;//智能交通设备有效，新报警信息类型
                struAlarmParam.byFaceAlarmDetection = 1;//1-人脸侦测
                //MessageBox.Show(struAlarmParam.dwSize.ToString());
                for (int i = 0; i < iDeviceNumber; i++)
                {
                    // 只对未布防或布防失败的设备进行布防
                    string deviceStatus = listViewDevice.Items[i].SubItems[2].Text;
                    if (deviceStatus != "布防成功")
                    {
                        m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                        m_lAlarmHandle[m_lUserID] = CHCNetSDK.NET_DVR_SetupAlarmChan_V41(m_lUserID, ref struAlarmParam);
                        if (m_lAlarmHandle[m_lUserID] < 0)
                        {
                            iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                            strErr = "布防失败，错误号：" + iLastErr; //布防失败，输出错误号
                            listViewDevice.Items[i].SubItems[2].Text = strErr;
                        }
                        else
                        {
                            //MessageBox.Show(struAlarmParam.dwSize.ToString());
                            listViewDevice.Items[i].SubItems[2].Text = "布防成功";  
                            
                            //布防成功之后传递消息
                            SendAlarmSuccessMessage(m_lUserID);
                        }
                    }
                }
                btn_SetAlarm.Enabled = false;
                //============================================================================================================================凌-全部布防
                //============================================================================================================================启动的时候添加两个IP并布防

            }

            // 设置 textBox2 的文本为当前日期，后面部分固定
            string currentDate = DateTime.Now.ToString("yyyy/MM/dd"); // 获取当前日期
            textBox2.Text = currentDate + " 18:18:18 工号-0001"; // 设置为当前日期和固定文本

            // 初始化上下文菜单
            contextMenuStrip = new ContextMenuStrip();
            sendMessageMenuItem = new ToolStripMenuItem("传递消息");
            sendMessageMenuItem.Click += SendMessageMenuItem_Click; // 绑定点击事件
            contextMenuStrip.Items.Add(sendMessageMenuItem);

            // 为 listViewAlarmInfo 添加右键菜单
            listViewAlarmInfo.ContextMenuStrip = contextMenuStrip;
        }

        public static class Constants
        {
            public static string CurrentTime { get; set; }

            // 静态构造函数用于初始化静态变量
            static Constants()
            {
                CurrentTime = DateTime.Now.ToString("HHmmss");
            }
        }

        public void UpdateClientListException(string strAlarmTime, int lUserID, string strAlarmMsg)
        {
            //异常设备信息
            string strDevIP = "";
            for (int i = 0; i < iDeviceNumber; i++)
            {
                m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                if (m_lUserID == lUserID)
                {
                    strDevIP = listViewDevice.Items[i].SubItems[1].Text.TrimEnd('\0');
                }
            }

            //列表新增报警信息
            listViewAlarmInfo.Items.Add(new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg }));

        }

        public void cbExceptionCB(uint dwType, int lUserID, int lHandle, IntPtr pUser)
        {
            //异常消息信息类型
            string stringAlarm = "异常消息回调，信息类型：0x" + Convert.ToString(dwType, 16) + ", lUserID:" + lUserID + ", lHandle:" + lHandle;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = lUserID;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallbackException(UpdateClientListException), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientListException(DateTime.Now.ToString(), lUserID, stringAlarm);
            }
        }

        public bool MsgCallback_V31(int lCommand, ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
           
            //通过lCommand来判断接收到的报警信息类型，不同的lCommand对应不同的pAlarmInfo内容
            AlarmMessageHandle(lCommand, ref pAlarmer, pAlarmInfo, dwBufLen, pUser);

            return true; //回调函数需要有返回，表示正常接收到数据
        }

        public void MsgCallback(int lCommand, ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            //通过lCommand来判断接收到的报警信息类型，不同的lCommand对应不同的pAlarmInfo内容
            AlarmMessageHandle(lCommand, ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
        }

        public void AlarmMessageHandle(int lCommand, ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
           // MessageBox.Show(sizeof(pAlarmer).ToString());
            //通过lCommand来判断接收到的报警信息类型，不同的lCommand对应不同的pAlarmInfo内容
            switch (lCommand)
            {
                case CHCNetSDK.COMM_ALARM: //(DS-8000老设备)移动侦测、视频丢失、遮挡、IO信号量等报警信息
                    ProcessCommAlarm(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_V30://移动侦测、视频丢失、遮挡、IO信号量等报警信息
                    ProcessCommAlarm_V30(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_RULE://进出区域、入侵、徘徊、人员聚集等异常行为识别报警信息
                    ProcessCommAlarm_RULE(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_UPLOAD_PLATE_RESULT://交通抓拍结果上传(老报警信息类型)
                    ProcessCommAlarm_Plate(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ITS_PLATE_RESULT://交通抓拍结果上传(新报警信息类型)
                    ProcessCommAlarm_ITSPlate(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_TPS_REAL_TIME://交通抓拍结果上传(新报警信息类型)
                    ProcessCommAlarm_TPSRealInfo(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_TPS_STATISTICS://交通抓拍结果上传(新报警信息类型)
                    ProcessCommAlarm_TPSStatInfo(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_PDC://客流量统计报警信息
                    ProcessCommAlarm_PDC(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;

                case CHCNetSDK.COMM_FIREDETECTION_ALARM://火点检测报警
                    ProcessCommAlarm_FireDetection(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_THERMOMETRY_ALARM://温度报警信息
                    ProcessCommAlarm_Thermometry(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;

                case CHCNetSDK.COMM_ITS_PARK_VEHICLE://客流量统计报警信息
                    ProcessCommAlarm_PARK(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_DIAGNOSIS_UPLOAD://VQD报警信息
                    ProcessCommAlarm_VQD(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_UPLOAD_FACESNAP_RESULT://人脸抓拍结果信息
                    ProcessCommAlarm_FaceSnap(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_SNAP_MATCH_ALARM://人脸比对结果信息
                    MessageBox.Show("人脸比对结果信息！");
                    ProcessCommAlarm_FaceMatch(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_FACE_DETECTION://人脸侦测报警信息
                    MessageBox.Show("人脸侦测报警信息！");
                    ProcessCommAlarm_FaceDetect(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARMHOST_CID_ALARM://报警主机CID报警上传
                    ProcessCommAlarm_CIDAlarm(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_UPLOAD_VIDEO_INTERCOM_EVENT://可视对讲事件记录信息
                    ProcessCommAlarm_InterComEvent(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ALARM_ACS://门禁主机报警上传
                    //MessageBox.Show("cccccccccccccccc");
                    ProcessCommAlarm_AcsAlarm(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ID_INFO_ALARM://身份证刷卡信息上传
                    ProcessCommAlarm_IDInfoAlarm(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_UPLOAD_AIOP_VIDEO://设备支持AI开放平台接入，上传视频检测数据
                    ProcessCommAlarm_AIOPVideo(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_UPLOAD_AIOP_PICTURE://设备支持AI开放平台接入，上传图片检测数据
                    ProcessCommAlarm_AIOPPicture(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                case CHCNetSDK.COMM_ISAPI_ALARM://ISAPI报警信息上传
                    //MessageBox.Show("ISAPI");
                    ProcessCommAlarm_ISAPIAlarm(ref pAlarmer, pAlarmInfo, dwBufLen, pUser);
                    break;
                default:
                    {
                        //报警设备IP地址
                        string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

                        //报警信息类型
                        string stringAlarm = "报警上传，信息类型：0x" + Convert.ToString(lCommand, 16);

                        if (InvokeRequired)
                        {
                            object[] paras = new object[3];
                            paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                            paras[1] = strIP;
                            paras[2] = stringAlarm;
                            listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
                        }
                        else
                        {
                            //创建该控件的主线程直接更新信息列表 
                            UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
                        }
                    }
                    break;
            }
        }

        public void ProcessCommAlarm(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_ALARMINFO struAlarmInfo = new CHCNetSDK.NET_DVR_ALARMINFO();

            struAlarmInfo = (CHCNetSDK.NET_DVR_ALARMINFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_ALARMINFO));

            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');
            string stringAlarm = "";
            int i = 0;

            switch (struAlarmInfo.dwAlarmType)
            {
                case 0:
                    stringAlarm = "信号量报警，报警报警输入口：" + struAlarmInfo.dwAlarmInputNumber + "，触发录像通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM; i++)
                    {
                        if (struAlarmInfo.dwAlarmRelateChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 1:
                    stringAlarm = "硬盘满，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM; i++)
                    {
                        if (struAlarmInfo.dwDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 2:
                    stringAlarm = "信号丢失，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM; i++)
                    {
                        if (struAlarmInfo.dwChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 3:
                    stringAlarm = "移动侦测，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM; i++)
                    {
                        if (struAlarmInfo.dwChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 4:
                    stringAlarm = "硬盘未格式化，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM; i++)
                    {
                        if (struAlarmInfo.dwDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 5:
                    stringAlarm = "读写硬盘出错，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM; i++)
                    {
                        if (struAlarmInfo.dwDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 6:
                    stringAlarm = "遮挡报警，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM; i++)
                    {
                        if (struAlarmInfo.dwChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 7:
                    stringAlarm = "制式不匹配，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM; i++)
                    {
                        if (struAlarmInfo.dwChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 8:
                    stringAlarm = "非法访问";
                    break;
                default:
                    stringAlarm = "其他未知报警信息";
                    break;
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString();
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_V30(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {

            CHCNetSDK.NET_DVR_ALARMINFO_V30 struAlarmInfoV30 = new CHCNetSDK.NET_DVR_ALARMINFO_V30();

            struAlarmInfoV30 = (CHCNetSDK.NET_DVR_ALARMINFO_V30)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_ALARMINFO_V30));

            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');
            string stringAlarm = "";
            int i;

            switch (struAlarmInfoV30.dwAlarmType)
            {
                case 0:
                    stringAlarm = "信号量报警，报警报警输入口：" + struAlarmInfoV30.dwAlarmInputNumber + "，触发录像通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byAlarmRelateChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + "\\";
                        }
                    }
                    break;
                case 1:
                    stringAlarm = "硬盘满，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " ";
                        }
                    }
                    break;
                case 2:
                    stringAlarm = "信号丢失，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 3:
                    stringAlarm = "移动侦测，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 4:
                    stringAlarm = "硬盘未格式化，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 5:
                    stringAlarm = "读写硬盘出错，报警硬盘号：";
                    for (i = 0; i < CHCNetSDK.MAX_DISKNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byDiskNumber[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 6:
                    stringAlarm = "遮挡报警，报警通道：";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 7:
                    stringAlarm = "制式不匹配，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 8:
                    stringAlarm = "非法访问";
                    break;
                case 9:
                    stringAlarm = "视频信号异常，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 10:
                    stringAlarm = "录像/抓图异常，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 11:
                    stringAlarm = "智能场景变化，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 12:
                    stringAlarm = "阵列异常";
                    break;
                case 13:
                    stringAlarm = "前端/录像分辨率不匹配，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                case 15:
                    stringAlarm = "智能侦测，报警通道";
                    for (i = 0; i < CHCNetSDK.MAX_CHANNUM_V30; i++)
                    {
                        if (struAlarmInfoV30.byChannel[i] == 1)
                        {
                            stringAlarm += (i + 1) + " \\ ";
                        }
                    }
                    break;
                default:
                    stringAlarm = "其他未知报警信息";
                    break;
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString();
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }

        }

        private void ProcessCommAlarm_RULE(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_VCA_RULE_ALARM struRuleAlarmInfo = new CHCNetSDK.NET_VCA_RULE_ALARM();
            struRuleAlarmInfo = (CHCNetSDK.NET_VCA_RULE_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_VCA_RULE_ALARM));

            //报警信息
            string stringAlarm = "";
            uint dwSize = (uint)Marshal.SizeOf(struRuleAlarmInfo.struRuleInfo.uEventParam);

            switch (struRuleAlarmInfo.struRuleInfo.wEventTypeEx)
            {
                case (ushort)CHCNetSDK.VCA_RULE_EVENT_TYPE_EX.ENUM_VCA_EVENT_TRAVERSE_PLANE:
                    IntPtr ptrTraverseInfo = Marshal.AllocHGlobal((Int32)dwSize);
                    Marshal.StructureToPtr(struRuleAlarmInfo.struRuleInfo.uEventParam, ptrTraverseInfo, false);
                    m_struTraversePlane = (CHCNetSDK.NET_VCA_TRAVERSE_PLANE)Marshal.PtrToStructure(ptrTraverseInfo, typeof(CHCNetSDK.NET_VCA_TRAVERSE_PLANE));
                    stringAlarm = "穿越警戒面，目标ID：" + struRuleAlarmInfo.struTargetInfo.dwID;
                    //警戒面边线起点坐标: (m_struTraversePlane.struPlaneBottom.struStart.fX, m_struTraversePlane.struPlaneBottom.struStart.fY)
                    //警戒面边线终点坐标: (m_struTraversePlane.struPlaneBottom.struEnd.fX, m_struTraversePlane.struPlaneBottom.struEnd.fY)
                    break;
                case (ushort)CHCNetSDK.VCA_RULE_EVENT_TYPE_EX.ENUM_VCA_EVENT_ENTER_AREA:
                    IntPtr ptrEnterInfo = Marshal.AllocHGlobal((Int32)dwSize);
                    Marshal.StructureToPtr(struRuleAlarmInfo.struRuleInfo.uEventParam, ptrEnterInfo, false);
                    m_struVcaArea = (CHCNetSDK.NET_VCA_AREA)Marshal.PtrToStructure(ptrEnterInfo, typeof(CHCNetSDK.NET_VCA_AREA));
                    stringAlarm = "目标进入区域，目标ID：" + struRuleAlarmInfo.struTargetInfo.dwID;
                    //m_struVcaArea.struRegion 多边形区域坐标
                    break;
                case (ushort)CHCNetSDK.VCA_RULE_EVENT_TYPE_EX.ENUM_VCA_EVENT_EXIT_AREA:
                    IntPtr ptrExitInfo = Marshal.AllocHGlobal((Int32)dwSize);
                    Marshal.StructureToPtr(struRuleAlarmInfo.struRuleInfo.uEventParam, ptrExitInfo, false);
                    m_struVcaArea = (CHCNetSDK.NET_VCA_AREA)Marshal.PtrToStructure(ptrExitInfo, typeof(CHCNetSDK.NET_VCA_AREA));
                    stringAlarm = "目标离开区域，目标ID：" + struRuleAlarmInfo.struTargetInfo.dwID;
                    //m_struVcaArea.struRegion 多边形区域坐标
                    break;
                case (ushort)CHCNetSDK.VCA_RULE_EVENT_TYPE_EX.ENUM_VCA_EVENT_INTRUSION:
                    IntPtr ptrIntrusionInfo = Marshal.AllocHGlobal((Int32)dwSize);
                    Marshal.StructureToPtr(struRuleAlarmInfo.struRuleInfo.uEventParam, ptrIntrusionInfo, false);
                    m_struIntrusion = (CHCNetSDK.NET_VCA_INTRUSION)Marshal.PtrToStructure(ptrIntrusionInfo, typeof(CHCNetSDK.NET_VCA_INTRUSION));

                    int i = 0;
                    string strRegion = "";
                    for (i = 0; i < m_struIntrusion.struRegion.dwPointNum; i++)
                    {
                        strRegion = strRegion + "(" + m_struIntrusion.struRegion.struPos[i].fX + "," + m_struIntrusion.struRegion.struPos[i].fY + ")";
                    }
                    stringAlarm = "周界入侵，目标ID：" + struRuleAlarmInfo.struTargetInfo.dwID + "，区域范围：" + strRegion;
                    //m_struIntrusion.struRegion 多边形区域坐标
                    break;
                default:
                    stringAlarm = "其他异常行为识别报警，目标ID：" + struRuleAlarmInfo.struTargetInfo.dwID;
                    break;
            }


            //报警图片保存
            if (struRuleAlarmInfo.dwPicDataLen > 0)
            {
                string str = ".\\picture\\UserID_" + pAlarmer.lUserID + "_异常行为识别_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struRuleAlarmInfo.dwPicDataLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struRuleAlarmInfo.pImage, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //报警时间：年月日时分秒
            string strTimeYear = ((struRuleAlarmInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((struRuleAlarmInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((struRuleAlarmInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((struRuleAlarmInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((struRuleAlarmInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((struRuleAlarmInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(struRuleAlarmInfo.struDevInfo.struDevIP.sIpV4).TrimEnd('\0');

            //将报警信息添加进列表
            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = strTime;
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(strTime, strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_Plate(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_PLATE_RESULT struPlateResultInfo = new CHCNetSDK.NET_DVR_PLATE_RESULT();
            uint dwSize = (uint)Marshal.SizeOf(struPlateResultInfo);

            struPlateResultInfo = (CHCNetSDK.NET_DVR_PLATE_RESULT)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_PLATE_RESULT));

            //保存抓拍图片
            string str = "";
            if (struPlateResultInfo.byResultType == 1 && struPlateResultInfo.dwPicLen != 0)
            {
                str = ".\\picture\\Plate_UserID_" + pAlarmer.lUserID + "_近景图_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struPlateResultInfo.dwPicLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struPlateResultInfo.pBuffer1, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }
            if (struPlateResultInfo.dwPicPlateLen != 0)
            {
                str = ".\\picture\\Plate_UserID_" + pAlarmer.lUserID + "_车牌图_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struPlateResultInfo.dwPicPlateLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struPlateResultInfo.pBuffer2, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }
            if (struPlateResultInfo.dwFarCarPicLen != 0)
            {
                str = ".\\picture\\Plate_UserID_" + pAlarmer.lUserID + "_远景图_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struPlateResultInfo.dwFarCarPicLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struPlateResultInfo.pBuffer5, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //抓拍时间：年月日时分秒
            string strTimeYear = System.Text.Encoding.UTF8.GetString(struPlateResultInfo.byAbsTime).TrimEnd('\0');

            //上传结果
            string stringPlateLicense = System.Text.Encoding.GetEncoding("GBK").GetString(struPlateResultInfo.struPlateInfo.sLicense).TrimEnd('\0');
            string stringAlarm = "抓拍上传，" + "车牌：" + stringPlateLicense + "，车辆序号：" + struPlateResultInfo.struVehicleInfo.dwIndex;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = strTimeYear; //当前PC系统时间为DateTime.Now.ToString();
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_ITSPlate(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_ITS_PLATE_RESULT struITSPlateResult = new CHCNetSDK.NET_ITS_PLATE_RESULT();
            uint dwSize = (uint)Marshal.SizeOf(struITSPlateResult);

            struITSPlateResult = (CHCNetSDK.NET_ITS_PLATE_RESULT)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_ITS_PLATE_RESULT));

            //保存抓拍图片
            for (int i = 0; i < struITSPlateResult.dwPicNum; i++)
            {
                if (struITSPlateResult.struPicInfo[i].dwDataLen != 0)
                {
                    string str = ".\\picture\\ITS_UserID_[" + pAlarmer.lUserID + "]_Pictype_" + struITSPlateResult.struPicInfo[i].byType
                        + "_PicNum[" + (i + 1) + "]_" + iFileNumber + ".jpg";
                    FileStream fs = new FileStream(str, FileMode.Create);
                    int iLen = (int)struITSPlateResult.struPicInfo[i].dwDataLen;
                    byte[] by = new byte[iLen];
                    Marshal.Copy(struITSPlateResult.struPicInfo[i].pBuffer, by, 0, iLen);
                    fs.Write(by, 0, iLen);
                    fs.Close();
                    iFileNumber++;
                }
            }
            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //抓拍时间：年月日时分秒
            string strTimeYear = string.Format("{0:D4}", struITSPlateResult.struSnapFirstPicTime.wYear) +
                string.Format("{0:D2}", struITSPlateResult.struSnapFirstPicTime.byMonth) +
                string.Format("{0:D2}", struITSPlateResult.struSnapFirstPicTime.byDay) + " "
                + string.Format("{0:D2}", struITSPlateResult.struSnapFirstPicTime.byHour) + ":"
                + string.Format("{0:D2}", struITSPlateResult.struSnapFirstPicTime.byMinute) + ":"
                + string.Format("{0:D2}", struITSPlateResult.struSnapFirstPicTime.bySecond) + ":"
                + string.Format("{0:D3}", struITSPlateResult.struSnapFirstPicTime.wMilliSec);

            //上传结果
            string stringPlateLicense = System.Text.Encoding.GetEncoding("GBK").GetString(struITSPlateResult.struPlateInfo.sLicense).TrimEnd('\0');
            string stringAlarm = "抓拍上传，" + "车牌：" + stringPlateLicense + "，车辆序号：" + struITSPlateResult.struVehicleInfo.dwIndex;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = strTimeYear;//当前系统时间为：DateTime.Now.ToString();
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }
        private void ProcessCommAlarm_TPSRealInfo(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_TPS_REAL_TIME_INFO struTPSInfo = new CHCNetSDK.NET_DVR_TPS_REAL_TIME_INFO();
            uint dwSize = (uint)Marshal.SizeOf(struTPSInfo);

            struTPSInfo = (CHCNetSDK.NET_DVR_TPS_REAL_TIME_INFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_TPS_REAL_TIME_INFO));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //抓拍时间：年月日时分秒
            string strTimeYear = string.Format("{0:D4}", struTPSInfo.struTime.wYear) +
                string.Format("{0:D2}", struTPSInfo.struTime.byMonth) +
                string.Format("{0:D2}", struTPSInfo.struTime.byDay) + " "
                + string.Format("{0:D2}", struTPSInfo.struTime.byHour) + ":"
                + string.Format("{0:D2}", struTPSInfo.struTime.byMinute) + ":"
                + string.Format("{0:D2}", struTPSInfo.struTime.bySecond) + ":"
                + string.Format("{0:D3}", struTPSInfo.struTime.wMilliSec);

            //上传结果
            string stringAlarm = "TPS实时过车数据，" + "通道号：" + struTPSInfo.dwChan +
                "，设备ID：" + struTPSInfo.struTPSRealTimeInfo.wDeviceID +
                "，开始码：" + struTPSInfo.struTPSRealTimeInfo.byStart +
                "，命令号：" + struTPSInfo.struTPSRealTimeInfo.byCMD +
                "，对应车道：" + struTPSInfo.struTPSRealTimeInfo.byLane +
                "，对应车速：" + struTPSInfo.struTPSRealTimeInfo.bySpeed +
                "，byLaneState：" + struTPSInfo.struTPSRealTimeInfo.byLaneState +
                "，byQueueLen：" + struTPSInfo.struTPSRealTimeInfo.byQueueLen +
                "，wLoopState：" + struTPSInfo.struTPSRealTimeInfo.wLoopState +
                "，wStateMask：" + struTPSInfo.struTPSRealTimeInfo.wStateMask +
                "，dwDownwardFlow：" + struTPSInfo.struTPSRealTimeInfo.dwDownwardFlow +
                "，dwUpwardFlow：" + struTPSInfo.struTPSRealTimeInfo.dwUpwardFlow +
                "，byJamLevel：" + struTPSInfo.struTPSRealTimeInfo.byJamLevel;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = strTimeYear;//当前系统时间为：DateTime.Now.ToString();
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_TPSStatInfo(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_TPS_STATISTICS_INFO struTPSStatInfo = new CHCNetSDK.NET_DVR_TPS_STATISTICS_INFO();
            uint dwSize = (uint)Marshal.SizeOf(struTPSStatInfo);

            struTPSStatInfo = (CHCNetSDK.NET_DVR_TPS_STATISTICS_INFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_TPS_STATISTICS_INFO));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //抓拍时间：年月日时分秒
            string strTimeYear = string.Format("{0:D4}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.wYear) +
                string.Format("{0:D2}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.byMonth) +
                string.Format("{0:D2}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.byDay) + " "
                + string.Format("{0:D2}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.byHour) + ":"
                + string.Format("{0:D2}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.byMinute) + ":"
                + string.Format("{0:D2}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.bySecond) + ":"
                + string.Format("{0:D3}", struTPSStatInfo.struTPSStatisticsInfo.struStartTime.wMilliSec);

            //上传结果
            string stringAlarm = "TPS统计过车数据，" + "通道号：" + struTPSStatInfo.dwChan +
                "，开始码：" + struTPSStatInfo.struTPSStatisticsInfo.byStart +
                "，命令号：" + struTPSStatInfo.struTPSStatisticsInfo.byCMD +
                "，统计开始时间：" + strTimeYear +
                "，统计时间(秒)：" + struTPSStatInfo.struTPSStatisticsInfo.dwSamplePeriod;


            for (int i = 0; i < CHCNetSDK.MAX_TPS_RULE; i++)
            {
                stringAlarm = stringAlarm + "车道号: " + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].byLane +
                    "，车道过车平均速度:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].bySpeed +
                    "，小型车数量:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].dwLightVehicle +
                    "，中型车数量:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].dwMidVehicle +
                    "，重型车数量:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].dwHeavyVehicle +
                    "，车头时距:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].dwTimeHeadway +
                    "，车头间距:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].dwSpaceHeadway +
                    "，空间占有率:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].fSpaceOccupyRation +
                    "，时间占有率:" + struTPSStatInfo.struTPSStatisticsInfo.struLaneParam[i].fTimeOccupyRation;
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString();//当前系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_FireDetection(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_FIREDETECTION_ALARM struFireDectionInfo = new CHCNetSDK.NET_DVR_FIREDETECTION_ALARM();
            uint dwSize = (uint)Marshal.SizeOf(struFireDectionInfo);
            struFireDectionInfo = (CHCNetSDK.NET_DVR_FIREDETECTION_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_FIREDETECTION_ALARM));

            //报警时间：年月日时分秒
            string strTimeYear = ((struFireDectionInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((struFireDectionInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((struFireDectionInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((struFireDectionInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((struFireDectionInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((struFireDectionInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "火点检测报警，触发时间：" + strTime + "，报警子类型：" + struFireDectionInfo.byAlarmSubType + ",火点最高温度：" + struFireDectionInfo.wFireMaxTemperature + ",火点目标距离：" + struFireDectionInfo.wTargetDistance;


            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');


            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }


        private void ProcessCommAlarm_Thermometry(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_THERMOMETRY_ALARM strThermometryInfo = new CHCNetSDK.NET_DVR_THERMOMETRY_ALARM();
            uint dwSize = (uint)Marshal.SizeOf(strThermometryInfo);
            strThermometryInfo = (CHCNetSDK.NET_DVR_THERMOMETRY_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_THERMOMETRY_ALARM));

            //报警时间：年月日时分秒
            string strTimeYear = ((strThermometryInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((strThermometryInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((strThermometryInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((strThermometryInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((strThermometryInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((strThermometryInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "温度报警，触发时间：" + strTime + "，通道：" + strThermometryInfo.dwChannel + ", 规则ID：" + strThermometryInfo.byRuleID + ",规则标定类型：" + strThermometryInfo.byRuleCalibType
                + "，温度:" + strThermometryInfo.fCurrTemperature;

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //保存可见光图片数据
            if ((strThermometryInfo.dwPicLen != 0) && (strThermometryInfo.pPicBuff != IntPtr.Zero))
            {
                string str = ".\\picture\\CapPic_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)strThermometryInfo.dwPicLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(strThermometryInfo.pPicBuff, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //保存可见光图片数据
            if ((strThermometryInfo.dwThermalPicLen != 0) && (strThermometryInfo.pThermalPicBuff != IntPtr.Zero))
            {
                string str = ".\\picture\\ThermometryPic_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)strThermometryInfo.dwThermalPicLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(strThermometryInfo.pThermalPicBuff, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }



        private void ProcessCommAlarm_PDC(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_PDC_ALRAM_INFO struPDCInfo = new CHCNetSDK.NET_DVR_PDC_ALRAM_INFO();
            uint dwSize = (uint)Marshal.SizeOf(struPDCInfo);
            struPDCInfo = (CHCNetSDK.NET_DVR_PDC_ALRAM_INFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_PDC_ALRAM_INFO));

            string stringAlarm = "客流量统计，进入人数：" + struPDCInfo.dwEnterNum + "，离开人数：" + struPDCInfo.dwLeaveNum;

            uint dwUnionSize = (uint)Marshal.SizeOf(struPDCInfo.uStatModeParam);
            IntPtr ptrPDCUnion = Marshal.AllocHGlobal((Int32)dwUnionSize);
            Marshal.StructureToPtr(struPDCInfo.uStatModeParam, ptrPDCUnion, false);

            if (struPDCInfo.byMode == 0) //单帧统计结果，此处为UTC时间
            {
                m_struStatFrame = (CHCNetSDK.UNION_STATFRAME)Marshal.PtrToStructure(ptrPDCUnion, typeof(CHCNetSDK.UNION_STATFRAME));
                stringAlarm = stringAlarm + "，单帧统计，相对时标：" + m_struStatFrame.dwRelativeTime + "，绝对时标：" + m_struStatFrame.dwAbsTime;
            }
            if (struPDCInfo.byMode == 1) //最小时间段统计结果
            {
                m_struStatTime = (CHCNetSDK.UNION_STATTIME)Marshal.PtrToStructure(ptrPDCUnion, typeof(CHCNetSDK.UNION_STATTIME));

                //开始时间
                string strStartTime = string.Format("{0:D4}", m_struStatTime.tmStart.dwYear) +
                string.Format("{0:D2}", m_struStatTime.tmStart.dwMonth) +
                string.Format("{0:D2}", m_struStatTime.tmStart.dwDay) + " "
                + string.Format("{0:D2}", m_struStatTime.tmStart.dwHour) + ":"
                + string.Format("{0:D2}", m_struStatTime.tmStart.dwMinute) + ":"
                + string.Format("{0:D2}", m_struStatTime.tmStart.dwSecond);

                //结束时间
                string strEndTime = string.Format("{0:D4}", m_struStatTime.tmEnd.dwYear) +
                string.Format("{0:D2}", m_struStatTime.tmEnd.dwMonth) +
                string.Format("{0:D2}", m_struStatTime.tmEnd.dwDay) + " "
                + string.Format("{0:D2}", m_struStatTime.tmEnd.dwHour) + ":"
                + string.Format("{0:D2}", m_struStatTime.tmEnd.dwMinute) + ":"
                + string.Format("{0:D2}", m_struStatTime.tmEnd.dwSecond);

                stringAlarm = stringAlarm + "，最小时间段统计，开始时间：" + strStartTime + "，结束时间：" + strEndTime;
            }
            Marshal.FreeHGlobal(ptrPDCUnion);

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');


            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }


        private void ProcessCommAlarm_PARK(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_ITS_PARK_VEHICLE struParkInfo = new CHCNetSDK.NET_ITS_PARK_VEHICLE();
            uint dwSize = (uint)Marshal.SizeOf(struParkInfo);
            struParkInfo = (CHCNetSDK.NET_ITS_PARK_VEHICLE)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_ITS_PARK_VEHICLE));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //保存抓拍图片
            for (int i = 0; i < struParkInfo.dwPicNum; i++)
            {
                if ((struParkInfo.struPicInfo[i].dwDataLen != 0) && (struParkInfo.struPicInfo[i].pBuffer != IntPtr.Zero))
                {
                    string str = ".\\picture\\Device_Park_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_Pictype_" + struParkInfo.struPicInfo[i].byType
                        + "_PicNum[" + (i + 1) + "]_" + iFileNumber + ".jpg";
                    FileStream fs = new FileStream(str, FileMode.Create);
                    int iLen = (int)struParkInfo.struPicInfo[i].dwDataLen;
                    byte[] by = new byte[iLen];
                    Marshal.Copy(struParkInfo.struPicInfo[i].pBuffer, by, 0, iLen);
                    fs.Write(by, 0, iLen);
                    fs.Close();
                    iFileNumber++;
                }
            }

            string stringAlarm = "停车场数据上传，异常状态：" + struParkInfo.byParkError + "，车位编号：" + struParkInfo.byParkingNo +
                ", 车辆状态：" + struParkInfo.byLocationStatus + "，车牌号码：" +
                System.Text.Encoding.GetEncoding("GBK").GetString(struParkInfo.struPlateInfo.sLicense).TrimEnd('\0');

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_VQD(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_DIAGNOSIS_UPLOAD struVQDInfo = new CHCNetSDK.NET_DVR_DIAGNOSIS_UPLOAD();
            uint dwSize = (uint)Marshal.SizeOf(struVQDInfo);
            struVQDInfo = (CHCNetSDK.NET_DVR_DIAGNOSIS_UPLOAD)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_DIAGNOSIS_UPLOAD));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //开始时间
            string strCheckTime = string.Format("{0:D4}", struVQDInfo.struCheckTime.dwYear) +
            string.Format("{0:D2}", struVQDInfo.struCheckTime.dwMonth) +
            string.Format("{0:D2}", struVQDInfo.struCheckTime.dwDay) + " "
            + string.Format("{0:D2}", struVQDInfo.struCheckTime.dwHour) + ":"
            + string.Format("{0:D2}", struVQDInfo.struCheckTime.dwMinute) + ":"
            + string.Format("{0:D2}", struVQDInfo.struCheckTime.dwSecond);

            string stringAlarm = "视频质量诊断结果，流ID：" + struVQDInfo.sStreamID + "，监测点IP：" + struVQDInfo.sMonitorIP + "，监测点通道号：" + struVQDInfo.dwChanIndex +
                "，检测时间：" + strCheckTime + "，byResult：" + struVQDInfo.byResult + "，bySignalResult：" + struVQDInfo.bySignalResult + "，byBlurResult：" + struVQDInfo.byBlurResult;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_FaceSnap(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_VCA_FACESNAP_RESULT struFaceSnapInfo = new CHCNetSDK.NET_VCA_FACESNAP_RESULT();
            uint dwSize = (uint)Marshal.SizeOf(struFaceSnapInfo);
            struFaceSnapInfo = (CHCNetSDK.NET_VCA_FACESNAP_RESULT)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_VCA_FACESNAP_RESULT));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = ((struFaceSnapInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((struFaceSnapInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((struFaceSnapInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((struFaceSnapInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((struFaceSnapInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((struFaceSnapInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "人脸抓拍结果，前端设备：" + System.Text.Encoding.UTF8.GetString(struFaceSnapInfo.struDevInfo.struDevIP.sIpV4).TrimEnd('\0') +
                "，通道号：" + struFaceSnapInfo.struDevInfo.byIvmsChannel + "，报警时间：" + strTime;


            if (struFaceSnapInfo.byAddInfo == 1)
            {
                CHCNetSDK.NET_VCA_FACESNAP_ADDINFO struFaceSnapAddInfo = new CHCNetSDK.NET_VCA_FACESNAP_ADDINFO();
                uint dwAddInfoSize = (uint)Marshal.SizeOf(struFaceSnapAddInfo);
                struFaceSnapAddInfo = (CHCNetSDK.NET_VCA_FACESNAP_ADDINFO)Marshal.PtrToStructure(struFaceSnapInfo.pAddInfoBuffer, typeof(CHCNetSDK.NET_VCA_FACESNAP_ADDINFO));

                stringAlarm = stringAlarm + ",fFaceTemperature:" + struFaceSnapAddInfo.fFaceTemperature + ",byThermometryUnit:" + struFaceSnapAddInfo.byThermometryUnit;
            }

            //保存抓拍图片数据
            if (enablePictureSaving && (struFaceSnapInfo.dwBackgroundPicLen != 0) && (struFaceSnapInfo.pBuffer2 != IntPtr.Zero))
            {
                // 使用新的保存方法，保存到D:\人脸识别照片\日期\工号.jpg
                bool saved = SaveFaceImage(struFaceSnapInfo.pBuffer2, (int)struFaceSnapInfo.dwBackgroundPicLen, stringAlarm, "FaceSnap");
                if (saved)
                {
                    iFileNumber++;
                }
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_FaceMatch(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM struFaceMatchAlarm = new CHCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM();
            uint dwSize = (uint)Marshal.SizeOf(struFaceMatchAlarm);
            struFaceMatchAlarm = (CHCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //保存抓拍库附加信息数据
            if ((struFaceMatchAlarm.struBlockListInfo.struBlockListInfo.dwFCAdditionInfoLen != 0) && (struFaceMatchAlarm.struBlockListInfo.struBlockListInfo.pFCAdditionInfoBuffer != IntPtr.Zero))
            {
                string str = ".\\picture\\FaceMatch_FCAdditionInfo_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".txt";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struFaceMatchAlarm.struBlockListInfo.struBlockListInfo.dwFCAdditionInfoLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struFaceMatchAlarm.struBlockListInfo.struBlockListInfo.pFCAdditionInfoBuffer, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //抓拍时间：年月日时分秒
            string strTimeYear = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((struFaceMatchAlarm.struSnapInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "人脸比对报警，抓拍设备：" + System.Text.Encoding.UTF8.GetString(struFaceMatchAlarm.struSnapInfo.struDevInfo.struDevIP.sIpV4).TrimEnd('\0') + "，抓拍时间："
                + strTime + "，相似度：" + struFaceMatchAlarm.fSimilarity;

            //保存抓拍人脸子图图片数据
            if (enablePictureSaving && (struFaceMatchAlarm.struSnapInfo.dwSnapFacePicLen != 0) && (struFaceMatchAlarm.struSnapInfo.pBuffer1 != IntPtr.Zero))
            {
                // 使用新的保存方法，保存到D:\人脸识别照片\日期\工号.jpg
                bool saved = SaveFaceImage(struFaceMatchAlarm.struSnapInfo.pBuffer1, (int)struFaceMatchAlarm.struSnapInfo.dwSnapFacePicLen, stringAlarm, "FaceMatch");
                if (saved)
                {
                    iFileNumber++;
                }
            }

            //保存比对结果人脸库人脸图片数据（这个通常是模板图片，不保存）
            //if (enablePictureSaving && (struFaceMatchAlarm.struBlockListInfo.dwBlockListPicLen != 0) && (struFaceMatchAlarm.struBlockListInfo.pBuffer1 != IntPtr.Zero))
            //{
            //    // 人脸库模板图片，通常不需要保存
            //}

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_FaceDetect(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_FACE_DETECTION struFaceDetectInfo = new CHCNetSDK.NET_DVR_FACE_DETECTION();
            uint dwSize = (uint)Marshal.SizeOf(struFaceDetectInfo);
            struFaceDetectInfo = (CHCNetSDK.NET_DVR_FACE_DETECTION)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_FACE_DETECTION));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0').TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = ((struFaceDetectInfo.dwAbsTime >> 26) + 2000).ToString();
            string strTimeMonth = ((struFaceDetectInfo.dwAbsTime >> 22) & 15).ToString("d2");
            string strTimeDay = ((struFaceDetectInfo.dwAbsTime >> 17) & 31).ToString("d2");
            string strTimeHour = ((struFaceDetectInfo.dwAbsTime >> 12) & 31).ToString("d2");
            string strTimeMinute = ((struFaceDetectInfo.dwAbsTime >> 6) & 63).ToString("d2");
            string strTimeSecond = ((struFaceDetectInfo.dwAbsTime >> 0) & 63).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "人脸抓拍结果结果，前端设备：" + System.Text.Encoding.UTF8.GetString(struFaceDetectInfo.struDevInfo.struDevIP.sIpV4) + "，报警时间：" + strTime;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_CIDAlarm(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_CID_ALARM struCIDAlarm = new CHCNetSDK.NET_DVR_CID_ALARM();
            uint dwSize = (uint)Marshal.SizeOf(struCIDAlarm);
            struCIDAlarm = (CHCNetSDK.NET_DVR_CID_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_CID_ALARM));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = (struCIDAlarm.struTriggerTime.wYear).ToString();
            string strTimeMonth = (struCIDAlarm.struTriggerTime.byMonth).ToString("d2");
            string strTimeDay = (struCIDAlarm.struTriggerTime.byDay).ToString("d2");
            string strTimeHour = (struCIDAlarm.struTriggerTime.byHour).ToString("d2");
            string strTimeMinute = (struCIDAlarm.struTriggerTime.byMinute).ToString("d2");
            string strTimeSecond = (struCIDAlarm.struTriggerTime.bySecond).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "报警主机CID报告，sCIDCode：" + System.Text.Encoding.UTF8.GetString(struCIDAlarm.sCIDCode).TrimEnd('\0')
                + "，sCIDDescribe：" + System.Text.Encoding.UTF8.GetString(struCIDAlarm.sCIDDescribe).TrimEnd('\0')
                + "，报告类型：" + struCIDAlarm.byReportType + "，防区号：" + struCIDAlarm.wDefenceNo + "，报警触发时间：" + strTime;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_InterComEvent(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_VIDEO_INTERCOM_EVENT struInterComEvent = new CHCNetSDK.NET_DVR_VIDEO_INTERCOM_EVENT();
            uint dwSize = (uint)Marshal.SizeOf(struInterComEvent);
            struInterComEvent = (CHCNetSDK.NET_DVR_VIDEO_INTERCOM_EVENT)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_VIDEO_INTERCOM_EVENT));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            if (struInterComEvent.byEventType == 3)
            {
                CHCNetSDK.NET_DVR_AUTH_INFO struAuthInfo = new CHCNetSDK.NET_DVR_AUTH_INFO();
                int dwUnionSize = Marshal.SizeOf(struInterComEvent.uEventInfo);
                IntPtr ptrAuthInfo = Marshal.AllocHGlobal(dwUnionSize);
                Marshal.StructureToPtr(struInterComEvent.uEventInfo, ptrAuthInfo, false);
                struAuthInfo = (CHCNetSDK.NET_DVR_AUTH_INFO)Marshal.PtrToStructure(ptrAuthInfo, typeof(CHCNetSDK.NET_DVR_AUTH_INFO));
                Marshal.FreeHGlobal(ptrAuthInfo);

                //保存抓拍图片
                if ((struAuthInfo.dwPicDataLen != 0) && (struAuthInfo.pImage != IntPtr.Zero))
                {
                    string str = ".\\picture\\Device_InterCom_CapturePic_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                    FileStream fs = new FileStream(str, FileMode.Create);
                    int iLen = (int)struAuthInfo.dwPicDataLen;
                    byte[] by = new byte[iLen];
                    Marshal.Copy(struAuthInfo.pImage, by, 0, iLen);
                    fs.Write(by, 0, iLen);
                    fs.Close();
                    iFileNumber++;
                }
            }

            //报警时间：年月日时分秒
            string strTimeYear = (struInterComEvent.struTime.wYear).ToString();
            string strTimeMonth = (struInterComEvent.struTime.byMonth).ToString("d2");
            string strTimeDay = (struInterComEvent.struTime.byDay).ToString("d2");
            string strTimeHour = (struInterComEvent.struTime.byHour).ToString("d2");
            string strTimeMinute = (struInterComEvent.struTime.byMinute).ToString("d2");
            string strTimeSecond = (struInterComEvent.struTime.bySecond).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "可视对讲事件，byEventType：" + struInterComEvent.byEventType + "，设备编号："
                + System.Text.Encoding.UTF8.GetString(struInterComEvent.byDevNumber).TrimEnd('\0') + "，报警触发时间：" + strTime;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }

        }

        private void ACS_remoteCheck(uint dwSerialNo)
        {
            CHCNetSDK.NET_DVR_XML_CONFIG_INPUT pInputXml = new CHCNetSDK.NET_DVR_XML_CONFIG_INPUT();
            Int32 nInSize = Marshal.SizeOf(pInputXml);
            pInputXml.dwSize = (uint)nInSize;

            string strRequestUrl = "PUT /ISAPI/AccessControl/remoteCheck?format=json";
            uint dwRequestUrlLen = (uint)strRequestUrl.Length;
            pInputXml.lpRequestUrl = Marshal.StringToHGlobalAnsi(strRequestUrl);
            pInputXml.dwRequestUrlLen = dwRequestUrlLen;

            string strInputParam = "{\"RemoteCheck\":{\"serialNo\":" + dwSerialNo + ",\"checkResult\":\"success\",\"info\":\"\"}}";

            pInputXml.lpInBuffer = Marshal.StringToHGlobalAnsi(strInputParam);
            pInputXml.dwInBufferSize = (uint)strInputParam.Length;

            CHCNetSDK.NET_DVR_XML_CONFIG_OUTPUT pOutputXml = new CHCNetSDK.NET_DVR_XML_CONFIG_OUTPUT();
            pOutputXml.dwSize = (uint)Marshal.SizeOf(pInputXml);
            pOutputXml.lpOutBuffer = Marshal.AllocHGlobal(3 * 1024 * 1024);
            pOutputXml.dwOutBufferSize = 3 * 1024 * 1024;
            pOutputXml.lpStatusBuffer = Marshal.AllocHGlobal(4096 * 4);
            pOutputXml.dwStatusSize = 4096 * 4;

            if (!CHCNetSDK.NET_DVR_STDXMLConfig(m_lUserID, ref pInputXml, ref pOutputXml))
            {
                iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                strErr = "NET_DVR_STDXMLConfig failed, error code= " + iLastErr;
                //XML透传失败，输出错误号 Failed to send XML data and output the error code
                MessageBox.Show(strErr);
            }
            else
            {
                //核验成功
            }

            Marshal.FreeHGlobal(pInputXml.lpRequestUrl);
            Marshal.FreeHGlobal(pOutputXml.lpOutBuffer);
            Marshal.FreeHGlobal(pOutputXml.lpStatusBuffer);
        }

        private void ProcessCommAlarm_AcsAlarm(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_ACS_ALARM_INFO struAcsAlarm = new CHCNetSDK.NET_DVR_ACS_ALARM_INFO();
            uint dwSize = (uint)Marshal.SizeOf(struAcsAlarm);
 
            struAcsAlarm = (CHCNetSDK.NET_DVR_ACS_ALARM_INFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_ACS_ALARM_INFO));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = (struAcsAlarm.struTime.dwYear).ToString();
            string strTimeMonth = (struAcsAlarm.struTime.dwMonth).ToString("d2");
            string strTimeDay = (struAcsAlarm.struTime.dwDay).ToString("d2");
            string strTimeHour = (struAcsAlarm.struTime.dwHour).ToString("d2");
            string strTimeMinute = (struAcsAlarm.struTime.dwMinute).ToString("d2");
            string strTimeSecond = (struAcsAlarm.struTime.dwSecond).ToString("d2");
            string strTime = strTimeYear + "/" + strTimeMonth + "/" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            //string stringAlarm = "门禁主机报警信息，dwMajor：0x" + Convert.ToString(struAcsAlarm.dwMajor, 16) + "，dwMinor：0x" +
            //    Convert.ToString(struAcsAlarm.dwMinor, 16) + "，卡号：" + System.Text.Encoding.UTF8.GetString(struAcsAlarm.struAcsEventInfo.byCardNo).TrimEnd('\0')
            //    + "，读卡器编号：" + struAcsAlarm.struAcsEventInfo.dwCardReaderNo + "，报警触发时间：" + strTime +
            //    "，事件流水号：" + struAcsAlarm.struAcsEventInfo.dwSerialNo;
            string stringAlarm = "报警触发时间：" + strTime; 
            //"，事件流水号：" + struAcsAlarm.struAcsEventInfo.dwSerialNo;

            if (struAcsAlarm.byAcsEventInfoExtend == 1)
            {
                CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND struInfoExtend = new CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND();
                uint dwSizeEx = (uint)Marshal.SizeOf(struInfoExtend);
                struInfoExtend = (CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND)Marshal.PtrToStructure(struAcsAlarm.pAcsEventInfoExtend, typeof(CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND));
                //stringAlarm = System.Text.Encoding.UTF8.GetString(struInfoExtend.byEmployeeNo).TrimEnd('\0');//只传递工号
                stringAlarm = stringAlarm + ", 工号-" + System.Text.Encoding.UTF8.GetString(struInfoExtend.byEmployeeNo).TrimEnd('\0');

                //MessageBox.Show(currentTimeString);

                 //if  ( strTimeHour  + strTimeMinute  + strTimeSecond ) //对比时间早于系统启动时间的忽略stringAlarm定义为空
                //MessageBox.Show( strTimeHour + strTimeMinute + strTimeSecond + "----"+ Constants.CurrentTime );
                int result = string.Compare(strTimeHour + strTimeMinute + strTimeSecond, Constants.CurrentTime);


                if (System.Text.Encoding.UTF8.GetString(struInfoExtend.byEmployeeNo).TrimEnd('\0') == "" )
                {
                    stringAlarm = "";
                }
                    else
                    {
                        if (result < 0) //如果是之前老的报警信息在此处另行处理
                       {
                           stringAlarm = stringAlarm.Replace("报警触发时间：", "*报警触发时间：");
                       }
                    }
                

                //if (result > 0)
                //{
                //    Console.WriteLine("有效时间！");
                // }

                //stringAlarm = stringAlarm + ", 工号:" + System.Text.Encoding.UTF8.GetString(struInfoExtend.byEmployeeNo).TrimEnd('\0') +
                //   ", 人员类型:" + struInfoExtend.byUserType;
            }

            //保存抓拍图片Acs 保存图片
            if (enablePictureSaving && (struAcsAlarm.dwPicDataLen != 0) && (struAcsAlarm.pPicData != IntPtr.Zero))
            {
                // 使用新的保存方法，保存到D:\人脸识别照片\日期\工号.jpg
                bool saved = SaveFaceImage(struAcsAlarm.pPicData, (int)struAcsAlarm.dwPicDataLen, stringAlarm, "ACS");
                if (saved)
                {
                    iFileNumber++;
                }
            }

            if (struAcsAlarm.byAcsEventInfoExtendV20 == 1)
            {
                CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND_V20 struInfoExtendV20 = new CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND_V20();
                uint dwSizeEx = (uint)Marshal.SizeOf(struInfoExtendV20);
                struInfoExtendV20 = (CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND_V20)Marshal.PtrToStructure(struAcsAlarm.pAcsEventInfoExtendV20, typeof(CHCNetSDK.NET_DVR_ACS_EVENT_INFO_EXTEND_V20));
                //stringAlarm = stringAlarm + ", 温度:" + struInfoExtendV20.fCurrTemperature + ", 是否异常温度:" + struInfoExtendV20.byIsAbnomalTemperature
                //   + ", 是否需要核验:" + struInfoExtendV20.byRemoteCheck;

                if (struInfoExtendV20.byRemoteCheck == 2)
                {
                    //建议另外建线程或者使用消息队列进行处理和下发核验结果给设备，避免阻塞回调
                    //ACS_remoteCheck(struAcsAlarm.struAcsEventInfo.dwSerialNo);
                }

                //保存热成像图片
                if ((struInfoExtendV20.dwThermalDataLen != 0) && (struInfoExtendV20.pThermalData != IntPtr.Zero))
                {
                    string str = ".\\picture\\Device_Acs_ThermalData_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                    FileStream fs = new FileStream(str, FileMode.Create);
                    int iLen = (int)struInfoExtendV20.dwThermalDataLen;
                    byte[] by = new byte[iLen];
                    Marshal.Copy(struInfoExtendV20.pThermalData, by, 0, iLen);
                    fs.Write(by, 0, iLen);
                    fs.Close();
                    iFileNumber++;
                }
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_IDInfoAlarm(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_ID_CARD_INFO_ALARM struIDInfoAlarm = new CHCNetSDK.NET_DVR_ID_CARD_INFO_ALARM();
            uint dwSize = (uint)Marshal.SizeOf(struIDInfoAlarm);
            struIDInfoAlarm = (CHCNetSDK.NET_DVR_ID_CARD_INFO_ALARM)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_ID_CARD_INFO_ALARM));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //保存抓拍图片
            if ((struIDInfoAlarm.dwCapturePicDataLen != 0) && (struIDInfoAlarm.pCapturePicData != IntPtr.Zero))
            {
                string str = ".\\picture\\Device_ID_CapturePic_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struIDInfoAlarm.dwCapturePicDataLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struIDInfoAlarm.pCapturePicData, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //保存身份证图片数据
            if ((struIDInfoAlarm.dwPicDataLen != 0) && (struIDInfoAlarm.pPicData != IntPtr.Zero))
            {
                string str = ".\\picture\\Device_ID_IDPic_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struIDInfoAlarm.dwPicDataLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struIDInfoAlarm.pPicData, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //保存指纹数据
            if ((struIDInfoAlarm.dwFingerPrintDataLen != 0) && (struIDInfoAlarm.pFingerPrintData != IntPtr.Zero))
            {
                string str = ".\\picture\\Device_ID_FingerPrint_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".data";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struIDInfoAlarm.dwFingerPrintDataLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struIDInfoAlarm.pFingerPrintData, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }

            //报警时间：年月日时分秒
            string strTimeYear = (struIDInfoAlarm.struSwipeTime.wYear).ToString();
            string strTimeMonth = (struIDInfoAlarm.struSwipeTime.byMonth).ToString("d2");
            string strTimeDay = (struIDInfoAlarm.struSwipeTime.byDay).ToString("d2");
            string strTimeHour = (struIDInfoAlarm.struSwipeTime.byHour).ToString("d2");
            string strTimeMinute = (struIDInfoAlarm.struSwipeTime.byMinute).ToString("d2");
            string strTimeSecond = (struIDInfoAlarm.struSwipeTime.bySecond).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "身份证刷卡信息，dwMajor：0x" + Convert.ToString(struIDInfoAlarm.dwMajor, 16) + "，dwMinor：0x" + Convert.ToString(struIDInfoAlarm.dwMinor)
                + "，身份证号：" + System.Text.Encoding.UTF8.GetString(struIDInfoAlarm.struIDCardCfg.byIDNum).TrimEnd('\0') +
                "，姓名：" + System.Text.Encoding.UTF8.GetString(struIDInfoAlarm.struIDCardCfg.byName).TrimEnd('\0') +
                "，刷卡时间：" + strTime;

            if (struIDInfoAlarm.byIDCardInfoExtend == 1)
            {
                CHCNetSDK.NET_DVR_ID_CARD_INFO_EXTEND struCardInfoExtend = new CHCNetSDK.NET_DVR_ID_CARD_INFO_EXTEND();
                uint dwSizeEx = (uint)Marshal.SizeOf(struCardInfoExtend);
                struCardInfoExtend = (CHCNetSDK.NET_DVR_ID_CARD_INFO_EXTEND)Marshal.PtrToStructure(struIDInfoAlarm.pIDCardInfoExtend, typeof(CHCNetSDK.NET_DVR_ID_CARD_INFO_EXTEND));
                stringAlarm = stringAlarm + ", 温度:" + struCardInfoExtend.fCurrTemperature + ", 是否异常温度:" + struCardInfoExtend.byIsAbnomalTemperature;

                //保存热成像图片
                if ((struCardInfoExtend.dwThermalDataLen != 0) && (struCardInfoExtend.pThermalData != IntPtr.Zero))
                {
                    string str = ".\\picture\\Device_IDINFO_ThermalData_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".jpg";
                    FileStream fs = new FileStream(str, FileMode.Create);
                    int iLen = (int)struCardInfoExtend.dwThermalDataLen;
                    byte[] by = new byte[iLen];
                    Marshal.Copy(struCardInfoExtend.pThermalData, by, 0, iLen);
                    fs.Write(by, 0, iLen);
                    fs.Close();
                    iFileNumber++;
                }
            }

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        private void ProcessCommAlarm_AIOPVideo(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_AIOP_VIDEO_HEAD struAIOPVideo = new CHCNetSDK.NET_AIOP_VIDEO_HEAD();
            uint dwSize = (uint)Marshal.SizeOf(struAIOPVideo);
            struAIOPVideo = (CHCNetSDK.NET_AIOP_VIDEO_HEAD)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_AIOP_VIDEO_HEAD));

            //报警设备struAIOPPic地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = (struAIOPVideo.struTime.wYear).ToString();
            string strTimeMonth = (struAIOPVideo.struTime.wMonth).ToString("d2");
            string strTimeDay = (struAIOPVideo.struTime.wDay).ToString("d2");
            string strTimeHour = (struAIOPVideo.struTime.wHour).ToString("d2");
            string strTimeMinute = (struAIOPVideo.struTime.wMinute).ToString("d2");
            string strTimeSecond = (struAIOPVideo.struTime.wSecond).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "AI开放平台视频检测报警上传，szTaskID：" + System.Text.Encoding.UTF8.GetString(struAIOPVideo.szTaskID).TrimEnd('\0')
                + ",报警触发时间：" + strTime;

            //保存AIOPData数据  
            if ((struAIOPVideo.dwAIOPDataSize != 0) && (struAIOPVideo.pBufferAIOPData != IntPtr.Zero))
            {
                string str = ".\\picture\\AiopData[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]" +
                     iFileNumber + ".txt";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struAIOPVideo.dwAIOPDataSize;
                byte[] by = new byte[iLen];
                Marshal.Copy(struAIOPVideo.pBufferAIOPData, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }
            //保存图片数据
            if ((struAIOPVideo.dwPictureSize != 0) && (struAIOPVideo.pBufferPicture != IntPtr.Zero))
            {
                string strPic = ".\\picture\\AiopPicture[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]" +
                     iFileNumber + ".jpg";
                FileStream fsPic = new FileStream(strPic, FileMode.Create);
                int iPicLen = (int)struAIOPVideo.dwPictureSize;
                byte[] byPic = new byte[iPicLen];
                Marshal.Copy(struAIOPVideo.pBufferPicture, byPic, 0, iPicLen);
                fsPic.Write(byPic, 0, iPicLen);
                fsPic.Close();
                iFileNumber++;
            }
            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }
        private void ProcessCommAlarm_AIOPPicture(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_AIOP_PICTURE_HEAD struAIOPPic = new CHCNetSDK.NET_AIOP_PICTURE_HEAD();
            uint dwSize = (uint)Marshal.SizeOf(struAIOPPic);
            struAIOPPic = (CHCNetSDK.NET_AIOP_PICTURE_HEAD)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_AIOP_PICTURE_HEAD));

            //报警设备struAIOPPic地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            //报警时间：年月日时分秒
            string strTimeYear = (struAIOPPic.struTime.wYear).ToString();
            string strTimeMonth = (struAIOPPic.struTime.wMonth).ToString("d2");
            string strTimeDay = (struAIOPPic.struTime.wDay).ToString("d2");
            string strTimeHour = (struAIOPPic.struTime.wHour).ToString("d2");
            string strTimeMinute = (struAIOPPic.struTime.wMinute).ToString("d2");
            string strTimeSecond = (struAIOPPic.struTime.wSecond).ToString("d2");
            string strTime = strTimeYear + "-" + strTimeMonth + "-" + strTimeDay + " " + strTimeHour + ":" + strTimeMinute + ":" + strTimeSecond;

            string stringAlarm = "AI开放平台图片检测报警上传，szPID：" + System.Text.Encoding.UTF8.GetString(struAIOPPic.szPID).TrimEnd('\0')
                + ",报警触发时间：" + strTime;

            //保存AIOPData数据  
            if ((struAIOPPic.dwAIOPDataSize != 0) && (struAIOPPic.pBufferAIOPData != IntPtr.Zero))
            {
                string str = ".\\picture\\AiopData[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]" +
                     iFileNumber + ".txt";
                FileStream fs = new FileStream(str, FileMode.Create);
                int iLen = (int)struAIOPPic.dwAIOPDataSize;
                byte[] by = new byte[iLen];
                Marshal.Copy(struAIOPPic.pBufferAIOPData, by, 0, iLen);
                fs.Write(by, 0, iLen);
                fs.Close();
                iFileNumber++;
            }
            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }
        private void ProcessCommAlarm_ISAPIAlarm(ref CHCNetSDK.NET_DVR_ALARMER pAlarmer, IntPtr pAlarmInfo, uint dwBufLen, IntPtr pUser)
        {
            CHCNetSDK.NET_DVR_ALARM_ISAPI_INFO struISAPIAlarm = new CHCNetSDK.NET_DVR_ALARM_ISAPI_INFO();
            uint dwSize = (uint)Marshal.SizeOf(struISAPIAlarm);
            struISAPIAlarm = (CHCNetSDK.NET_DVR_ALARM_ISAPI_INFO)Marshal.PtrToStructure(pAlarmInfo, typeof(CHCNetSDK.NET_DVR_ALARM_ISAPI_INFO));

            //报警设备IP地址
            string strIP = System.Text.Encoding.UTF8.GetString(pAlarmer.sDeviceIP).TrimEnd('\0');

            // 提取XML或JSON数据用于工号解析
            string alarmDataString = "";
            if ((struISAPIAlarm.dwAlarmDataLen != 0) && (struISAPIAlarm.pAlarmData != IntPtr.Zero))
            {
                int iLen = (int)struISAPIAlarm.dwAlarmDataLen;
                byte[] by = new byte[iLen];
                Marshal.Copy(struISAPIAlarm.pAlarmData, by, 0, iLen);
                alarmDataString = System.Text.Encoding.UTF8.GetString(by);

                // 如果启用图片保存，也保存XML/JSON数据文件（可选）
                if (enablePictureSaving)
                {
                    string str = "";
                    if (struISAPIAlarm.byDataType == 1) // 0-invalid,1-xml,2-json
                    {
                        str = ".\\picture\\ISAPI_Alarm_XmlData_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".xml";
                    }
                    if (struISAPIAlarm.byDataType == 2) // 0-invalid,1-xml,2-json
                    {
                        str = ".\\picture\\ISAPI_Alarm_JsonData_[" + strIP + "]_lUerID_[" + pAlarmer.lUserID + "]_" + iFileNumber + ".json";
                    }

                    if (!string.IsNullOrEmpty(str))
                    {
                        FileStream fs = new FileStream(str, FileMode.Create);
                        fs.Write(by, 0, iLen);
                        fs.Close();
                        iFileNumber++;
                    }
                }
            }



            for (int i = 0; i < struISAPIAlarm.byPicturesNumber; i++)
            {
                CHCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA struPicData = new CHCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA();
                struPicData.szFilename = new byte[256];
                Int32 nSize = Marshal.SizeOf(struPicData);
                struPicData = (CHCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA)Marshal.PtrToStructure((IntPtr)((Int64)(struISAPIAlarm.pPicPackData) + i * nSize), typeof(CHCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA));

                //保存图片数据
                if (enablePictureSaving && (struPicData.dwPicLen != 0) && (struPicData.pPicData != IntPtr.Zero))
                {
                    // 使用新的保存方法，保存到D:\人脸识别照片\日期\工号.jpg
                    bool saved = SaveFaceImage(struPicData.pPicData, (int)struPicData.dwPicLen, alarmDataString, "ISAPI");
                    if (saved)
                    {
                        iFileNumber++;
                    }
                }
            }

            string stringAlarm = "ISAPI报警信息，byDataType：" + struISAPIAlarm.byDataType + "，图片张数：" + struISAPIAlarm.byPicturesNumber;

            if (InvokeRequired)
            {
                object[] paras = new object[3];
                paras[0] = DateTime.Now.ToString(); //当前PC系统时间
                paras[1] = strIP;
                paras[2] = stringAlarm;
                listViewAlarmInfo.BeginInvoke(new UpdateListBoxCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(DateTime.Now.ToString(), strIP, stringAlarm);
            }
        }

        public void UpdateClientList(string strAlarmTime, string strDevIP, string strAlarmMsg)
        {
            //列表新增报警信息

            if (strAlarmMsg == "")
            {
              //listViewAlarmInfo.Items.Add(new ListViewItem(new string[] { strAlarmTime, strDevIP, "无刷脸消息" }));
            }
            else
            {
                // 加载用户照片 注释掉，避免频繁显示照片
                // LoadUserPhoto(strAlarmMsg);
                
                //Process P = Process.Start("D:\\1.exe", strAlarmMsg);

                string result = strAlarmMsg.Replace("报警触发时间：", "").Replace(",", "");
                //string result = Regex.Replace(strAlarmMsg, @"[\u4e00-\u9fa5\p{P}]+", "");
                //MessageBox.Show(result);


                // 将 result 写入到文件
                // 获取当前日期作为文件名的前缀
                string currentDatePrefix = DateTime.Now.ToString("yyyyMMdd");
                string fileName = currentDatePrefix + "人脸识别消息.txt";

                // 获取临时文件夹路径
                string tempFolderPath = Path.GetTempPath();
                string filePath = Path.Combine(tempFolderPath, fileName);


                try
                {
                    // 将内容追加到临时文件
                    using (StreamWriter sw = new StreamWriter(filePath, true, Encoding.UTF8))
                    {
                        sw.WriteLine(result);
                    }

                    //MessageBox.Show("写入文件成功！");
                }
                catch (Exception ex)
                {
                    //MessageBox.Show("写入文件时发生错误：" + ex.Message);
                    Console.WriteLine("写入文件时发生错误：" + ex.Message);
                }

                // 使用文本框中的窗口名称
                string window1Name = textBoxWindow1.Text.Trim();
                string window2Name = textBoxWindow2.Text.Trim();

                int hWnd = FindWindow(null, window1Name);
                if (hWnd == 0)
                {
                    int hWnd2 = FindWindow(null, window2Name);
                    if (hWnd2 == 0)
                    {
                        //MessageBox.Show("555，未找到消息接受者！");
                        // 设置文本颜色为红色
                        textBox2.ForeColor = Color.Red;

                        // 添加报警信息到列表，设置为红色
                        ListViewItem item = new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg });
                        item.ForeColor = Color.Red; // 设置报警信息为红色
                        listViewAlarmInfo.Items.Add(item);

                        // 记录失败消息到文件
                        LogFailedMessage(result);
                    }
                    else
                    {
                        byte[] sarr = System.Text.Encoding.Default.GetBytes(strAlarmTime);
                        int len = sarr.Length;
                        COPYDATASTRUCT cds;
                        cds.dwData = (IntPtr)Convert.ToInt16(0);//可以是任意值  
                        cds.cbData = len + 25;//指定lpData内存区域的字节数
                        cds.lpData = result;//发送给目标窗口所在进程的数据  
                        int sendResult = SendMessage(hWnd2, WM_COPYDATA, 0, ref cds);

                        // 判断 SendMessage 是否成功
                        if (sendResult != 0)
                        {
                            textBox2.ForeColor = Color.Green; // 设置文本颜色为绿色

                            // 添加报警信息到列表，设置为绿色
                            ListViewItem item = new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg });
                            item.ForeColor = Color.Green; // 设置报警信息为绿色
                            listViewAlarmInfo.Items.Add(item);
                        }
                        else
                        {
                            textBox2.ForeColor = Color.Red; // 设置文本颜色为红色

                            // 添加报警信息到列表，设置为红色
                            ListViewItem item = new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg });
                            item.ForeColor = Color.Red; // 设置报警信息为红色
                            listViewAlarmInfo.Items.Add(item);

                            // 记录失败消息到文件
                            LogFailedMessage(result);
                        }
                    }
                }
                else
                {
                    byte[] sarr = System.Text.Encoding.Default.GetBytes(strAlarmTime);
                    int len = sarr.Length;
                    COPYDATASTRUCT cds;
                    cds.dwData = (IntPtr)Convert.ToInt16(0);//可以是任意值  
                    cds.cbData = len + 25;//指定lpData内存区域的字节数len + 20的时候chengshouzhuang漏掉最后一个字母
                    cds.lpData = result;//发送给目标窗口所在进程的数据  
                    int sendResult = SendMessage(hWnd, WM_COPYDATA, 0, ref cds);

                    // 判断 SendMessage 是否成功
                    if (sendResult != 0)
                    {
                        textBox2.ForeColor = Color.Green; // 设置文本颜色为绿色

                        // 添加报警信息到列表，设置为绿色
                        ListViewItem item = new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg });
                        item.ForeColor = Color.Green; // 设置报警信息为绿色
                        listViewAlarmInfo.Items.Add(item);
                    }
                    else
                    {
                        textBox2.ForeColor = Color.Red; // 设置文本颜色为红色

                        // 添加报警信息到列表，设置为红色
                        ListViewItem item = new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg });
                        item.ForeColor = Color.Red; // 设置报警信息为红色
                        listViewAlarmInfo.Items.Add(item);

                        // 记录失败消息到文件
                        LogFailedMessage(result);
                    }
                }

                //listViewAlarmInfo.Items.Add(new ListViewItem(new string[] { strAlarmTime, strDevIP, strAlarmMsg }));//先传递消息后添加，也可以注释掉本行！
                textBox2.Text = strAlarmMsg;

                // 自动滚动到最底部
                if (listViewAlarmInfo.Items.Count > 0)
                {
                    listViewAlarmInfo.EnsureVisible(listViewAlarmInfo.Items.Count - 1);
                    listViewAlarmInfo.Items[listViewAlarmInfo.Items.Count - 1].Selected = true;
                }
            }
        }

        public void UpdateClientList(string strLogStatus, IntPtr lpDeviceInfo)
        {
            //列表新增报警信息
            labelLogin.Text = "登录状态（异步）：" + strLogStatus;
        }

        public void cbLoginCallBack(int lUserID, int dwResult, IntPtr lpDeviceInfo, IntPtr pUser)
        {
            string strLoginCallBack = "登录设备，lUserID：" + lUserID + "，dwResult：" + dwResult;
            //MessageBox.Show(strLoginCallBack);
            if (dwResult == 0)
            {
                uint iErrCode = CHCNetSDK.NET_DVR_GetLastError();
                strLoginCallBack = strLoginCallBack + "，错误号:" + iErrCode;
            }

            //下面代码注释掉也会崩溃
            if (InvokeRequired)
            {
                object[] paras = new object[2];
                paras[0] = strLoginCallBack;
                paras[1] = lpDeviceInfo;
                labelLogin.BeginInvoke(new UpdateTextStatusCallback(UpdateClientList), paras);
            }
            else
            {
                //创建该控件的主线程直接更新信息列表 
                UpdateClientList(strLoginCallBack, lpDeviceInfo);
            }
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            //============================================================================================================================凌-添加IP1
            if (textBoxIP.Text == "" || textBoxPort.Text == "" ||
                textBoxUserName.Text == "" || textBoxPassword.Text == "")
            {
                MessageBox.Show("请输入IP地址、端口、用户名和密码！");
                return;
            }

            if (iDeviceNumber >= 20)
            {
                MessageBox.Show("本程序限制最多添加20台设备！");
                return;
            }
            
            // 检查IP是否已存在且布防成功
            bool ipExists = false;
            for (int i = 0; i < iDeviceNumber; i++)
            {
                string existingIP = listViewDevice.Items[i].SubItems[1].Text;
                string deviceStatus = listViewDevice.Items[i].SubItems[2].Text;
                
                if (existingIP == textBoxIP.Text && deviceStatus == "布防成功")
                {
                    ipExists = true;
                    break;
                }
            }
            
            if (ipExists)
            {
                MessageBox.Show("重复设备IP，且已布防成功，不再添加！");
                return;
            }

            CHCNetSDK.NET_DVR_USER_LOGIN_INFO struLogInfo = new CHCNetSDK.NET_DVR_USER_LOGIN_INFO();

            //设备IP地址或者域名
            byte[] byIP = System.Text.Encoding.Default.GetBytes(textBoxIP.Text);
            struLogInfo.sDeviceAddress = new byte[129];
            byIP.CopyTo(struLogInfo.sDeviceAddress, 0);

            //设备用户名
            byte[] byUserName = System.Text.Encoding.Default.GetBytes(textBoxUserName.Text);
            struLogInfo.sUserName = new byte[64];
            byUserName.CopyTo(struLogInfo.sUserName, 0);

            //设备密码
            byte[] byPassword = System.Text.Encoding.Default.GetBytes(textBoxPassword.Text);
            struLogInfo.sPassword = new byte[64];
            byPassword.CopyTo(struLogInfo.sPassword, 0);

            struLogInfo.wPort = ushort.Parse(textBoxPort.Text);//设备服务端口号

            struLogInfo.cbLoginResult = LoginCallBack;
            struLogInfo.bUseAsynLogin = false; //是否异步登录：0- 否，1- 是 

            if ((struLogInfo.bUseAsynLogin == true) && (LoginCallBack == null))
            {
                LoginCallBack = new CHCNetSDK.LOGINRESULTCALLBACK(cbLoginCallBack);//注册回调函数                    
            }

            struLogInfo.byLoginMode = 0; //0-Private, 1-ISAPI, 2-自适应
            struLogInfo.byHttps = 0; //0-不适用tls，1-使用tls 2-自适应

            CHCNetSDK.NET_DVR_DEVICEINFO_V40 DeviceInfo = new CHCNetSDK.NET_DVR_DEVICEINFO_V40();

            //登录设备 Login the device
            m_lUserID = CHCNetSDK.NET_DVR_Login_V40(ref struLogInfo, ref DeviceInfo);
            if (m_lUserID < 0)
            {
                iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                strErr = "登录失败，错误号： " + iLastErr; 
                MessageBox.Show(strErr);
            }
            else
            {
                //登录成功
                iDeviceNumber++;
                string str1 = "" + m_lUserID;
                
                // 根据IP地址添加备注
                string remark = "";
                if (textBoxIP.Text == "***********")
                {
                    remark = "小学";
                }
                else if (textBoxIP.Text == "***********")
                {
                    remark = "初中";
                }
                
                listViewDevice.Items.Add(new ListViewItem(new string[] { str1, textBoxIP.Text, "未布防", remark }));//将已注册设备添加进列表
            }

            //============================================================================================================================凌-全部布防
            CHCNetSDK.NET_DVR_SETUPALARM_PARAM struAlarmParam = new CHCNetSDK.NET_DVR_SETUPALARM_PARAM();
            struAlarmParam.dwSize = (uint)Marshal.SizeOf(struAlarmParam);
            struAlarmParam.byLevel = 1; //0- 一级布防,1- 二级布防
            struAlarmParam.byAlarmInfoType = 1;//智能交通设备有效，新报警信息类型
            struAlarmParam.byFaceAlarmDetection = 1;//1-人脸侦测
            //MessageBox.Show(struAlarmParam.dwSize.ToString());
            for (int i = 0; i < iDeviceNumber; i++)
            {
                // 只对未布防或布防失败的设备进行布防
                string deviceStatus = listViewDevice.Items[i].SubItems[2].Text;
                if (deviceStatus != "布防成功")
                {
                    m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                    m_lAlarmHandle[m_lUserID] = CHCNetSDK.NET_DVR_SetupAlarmChan_V41(m_lUserID, ref struAlarmParam);
                    if (m_lAlarmHandle[m_lUserID] < 0)
                    {
                        iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                        strErr = "布防失败，错误号：" + iLastErr; //布防失败，输出错误号
                        listViewDevice.Items[i].SubItems[2].Text = strErr;
                    }
                    else
                    {
                        //MessageBox.Show(struAlarmParam.dwSize.ToString());
                        listViewDevice.Items[i].SubItems[2].Text = "布防成功";  
                        
                        //布防成功之后传递消息
                        SendAlarmSuccessMessage(m_lUserID);
                    }
                }
            }
            btn_SetAlarm.Enabled = false;
            //============================================================================================================================凌-全部布防
        }

        private void btn_SetAlarm_Click(object sender, EventArgs e)
        {
            CHCNetSDK.NET_DVR_SETUPALARM_PARAM struAlarmParam = new CHCNetSDK.NET_DVR_SETUPALARM_PARAM();
            struAlarmParam.dwSize = (uint)Marshal.SizeOf(struAlarmParam);
            struAlarmParam.byLevel = 1; //0- 一级布防,1- 二级布防
            struAlarmParam.byAlarmInfoType = 1;//智能交通设备有效，新报警信息类型
            struAlarmParam.byFaceAlarmDetection = 1;//1-人脸侦测
            //MessageBox.Show(struAlarmParam.dwSize.ToString());
            for (int i = 0; i < iDeviceNumber; i++)
            {
                // 只对未布防或布防失败的设备进行布防
                string deviceStatus = listViewDevice.Items[i].SubItems[2].Text;
                if (deviceStatus != "布防成功")
                {
                    m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                    m_lAlarmHandle[m_lUserID] = CHCNetSDK.NET_DVR_SetupAlarmChan_V41(m_lUserID, ref struAlarmParam);
                    if (m_lAlarmHandle[m_lUserID] < 0)
                    {
                        iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                        strErr = "布防失败，错误号：" + iLastErr; //布防失败，输出错误号
                        listViewDevice.Items[i].SubItems[2].Text = strErr;
                    }
                    else
                    {
                        //MessageBox.Show(struAlarmParam.dwSize.ToString());
                        listViewDevice.Items[i].SubItems[2].Text = "布防成功";  
                        
                        //布防成功之后传递消息
                        SendAlarmSuccessMessage(m_lUserID);
                    }
                }
            }
            btn_SetAlarm.Enabled = false;
        }

        private void btnCloseAlarm_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < iDeviceNumber; i++)
            {
                m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                if (m_lAlarmHandle[m_lUserID] >= 0)
                {
                    if (!CHCNetSDK.NET_DVR_CloseAlarmChan_V30(m_lAlarmHandle[m_lUserID]))
                    {
                        iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                        strErr = "撤防失败，错误号：" + iLastErr; //撤防失败，输出错误号
                        listViewDevice.Items[i].SubItems[2].Text = strErr;
                    }
                    else
                    {
                        listViewDevice.Items[i].SubItems[2].Text = "未布防";
                        m_lAlarmHandle[i] = -1;
                    }
                }
                else
                {
                    listViewDevice.Items[i].SubItems[2].Text = "未布防";
                }
            }
            btn_SetAlarm.Enabled = true;
        }

        private void listViewDevice_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                if (listViewDevice.SelectedItems.Count > 0)
                {
                    if (DialogResult.OK == MessageBox.Show("请确认是否删除所选择的设备！", "删除提示", MessageBoxButtons.OKCancel))
                    {
                        foreach (ListViewItem item in this.listViewDevice.SelectedItems)
                        {
                            listViewDevice.Items.Remove(item);
                            iDeviceNumber--;
                        }
                    }
                }
            }
        }

        private void btnStartListen_Click(object sender, EventArgs e)
        {
            string sLocalIP = textBoxListenIP.Text;
            ushort wLocalPort = ushort.Parse(textBoxListenPort.Text);

            if (m_falarmData == null)
            {
                m_falarmData = new CHCNetSDK.MSGCallBack(MsgCallback);
            }

            iListenHandle = CHCNetSDK.NET_DVR_StartListen_V30(sLocalIP, wLocalPort, m_falarmData, IntPtr.Zero);
            if (iListenHandle < 0)
            {
                iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                strErr = "启动监听失败，错误号：" + iLastErr; //撤防失败，输出错误号
                MessageBox.Show(strErr);
            }
            else
            {
                MessageBox.Show("成功启动监听！");
                btnStopListen.Enabled = true;
                btnStartListen.Enabled = false;
            }
        }

        private void btnStopListen_Click(object sender, EventArgs e)
        {
            if (!CHCNetSDK.NET_DVR_StopListen_V30(iListenHandle))
            {
                iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                strErr = "停止监听失败，错误号：" + iLastErr; //撤防失败，输出错误号
                MessageBox.Show(strErr);
            }
            else
            {
                MessageBox.Show("停止监听！");
                btnStopListen.Enabled = false;
                btnStartListen.Enabled = true;
            }
        }

        private void AlarmDemo_Load(object sender, EventArgs e)
        {
            // 设置照片控件的初始可见性
            picUserPhoto.Visible = true;

            // 加载默认照片
            try
            {
                string defaultPhotoPath = Path.Combine(Application.StartupPath, "老师照片", "0001.jpg");
                if (File.Exists(defaultPhotoPath))
                {
                    using (FileStream fs = new FileStream(defaultPhotoPath, FileMode.Open, FileAccess.Read))
                    {
                        picUserPhoto.Image = Image.FromStream(fs);
                        currentPhotoPath = defaultPhotoPath; // 保存当前照片路径
                    }
                }
            }
            catch (Exception ex)
            {
                if (enableErrorLogging)
                {
                    LogError("加载默认照片时出错: " + ex.Message);
                }
            }

            //设置listViewAlarmInfo每列的宽度
            //listViewAlarmInfo.Columns[0].Width = 160;
            //listViewAlarmInfo.Columns[1].Width = 120;
            //listViewAlarmInfo.Columns[2].Width = 380;

            // 由于复选框已经在设计器中添加，Load方法不需要再动态创建它
            // 如果需要，可以在这里放置其他初始化代码
            
            // 确保照片文件夹存在
            string teacherPhotoFolder = Path.Combine(Application.StartupPath, "老师照片");
            string studentPhotoFolder = Path.Combine(Application.StartupPath, "学生照片");

            if (!Directory.Exists(teacherPhotoFolder))
            {
                Directory.CreateDirectory(teacherPhotoFolder);
            }
            
            if (!Directory.Exists(studentPhotoFolder))
            {
                Directory.CreateDirectory(studentPhotoFolder);
            }
            
            // 初始化时确保照片控件显示在报警布防标签页
            if (mainTabControl.SelectedIndex == 0) // 如果当前是报警布防标签页
            {
                picUserPhoto.Visible = true; // 显示照片
            }
            else
            {
                picUserPhoto.Visible = false; // 隐藏照片
            }
            
            // 启动时检查是否有传递失败的消息需要重新处理
            try
            {
                // 获取桌面路径和当天日期
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string datePrefix = DateTime.Now.ToString("yyyyMMdd");
                string filePath = Path.Combine(desktopPath, datePrefix + "传递失败人脸消息.txt");
                
                // 检查文件是否存在
                if (File.Exists(filePath))
                {
                    // 读取所有失败消息
                    string[] failedMessages = File.ReadAllLines(filePath);
                    
                    if (failedMessages.Length > 0)
                    {
                        // 将消息添加到列表视图中
                        foreach (string message in failedMessages)
                        {
                            if (!string.IsNullOrEmpty(message))
                            {
                                // 创建一个新的列表项
                                ListViewItem item = new ListViewItem(new string[] 
                                { 
                                    DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss"),  // 当前时间
                                    "重试消息",                                   // 显示这是重试的消息
                                    message                                       // 消息内容
                                });
                                
                                // 设置为红色表示之前传递失败
                                item.ForeColor = Color.Red;
                                
                                // 添加到列表中
                                listViewAlarmInfo.Items.Add(item);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误，但不影响程序启动
                if (enableErrorLogging)
                {
                    LogError("读取失败消息时发生错误：" + ex.Message);
                }
            }
        }
        
        // 复选框状态改变事件处理程序
        private void chkEnableErrorLog_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox chk = sender as CheckBox;
            if (chk != null)
            {
                enableErrorLogging = chk.Checked;
            }
        }

        // 保存图片复选框状态改变事件处理程序
        private void chkSavePictures_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox chk = sender as CheckBox;
            if (chk != null)
            {
                enablePictureSaving = chk.Checked;
            }
        }

        /// <summary>
        /// 保存人脸识别图片到指定目录
        /// </summary>
        /// <param name="imageData">图片数据</param>
        /// <param name="dataLength">数据长度</param>
        /// <param name="alarmMessage">报警消息（用于提取工号）</param>
        /// <param name="imageType">图片类型（如"FaceSnap", "FaceMatch"等）</param>
        /// <returns>是否保存成功</returns>
        private bool SaveFaceImage(IntPtr imageData, int dataLength, string alarmMessage, string imageType = "Face")
        {
            try
            {
                if (!enablePictureSaving || imageData == IntPtr.Zero || dataLength <= 0)
                {
                    return false;
                }

                // 从报警消息中提取工号
                string employeeId = ExtractEmployeeId(alarmMessage);
                if (string.IsNullOrEmpty(employeeId))
                {
                    if (enableErrorLogging)
                    {
                        LogError($"无法从报警消息中提取工号: {alarmMessage}");
                    }
                    return false;
                }

                // 创建保存路径：D:\人脸识别照片\20250804\
                string currentDate = DateTime.Now.ToString("yyyyMMdd");
                string saveDirectory = Path.Combine(@"D:\人脸识别照片", currentDate);

                // 确保目录存在
                if (!Directory.Exists(saveDirectory))
                {
                    Directory.CreateDirectory(saveDirectory);
                }

                // 构建文件名：工号.jpg
                string fileName = $"{employeeId}.jpg";
                string filePath = Path.Combine(saveDirectory, fileName);

                // 保存图片数据
                using (FileStream fs = new FileStream(filePath, FileMode.Create))
                {
                    byte[] imageBytes = new byte[dataLength];
                    Marshal.Copy(imageData, imageBytes, 0, dataLength);
                    fs.Write(imageBytes, 0, dataLength);
                }

                if (enableErrorLogging)
                {
                    LogError($"成功保存{imageType}图片: {filePath}");
                }

                return true;
            }
            catch (Exception ex)
            {
                if (enableErrorLogging)
                {
                    LogError($"保存{imageType}图片时出错: {ex.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// 从报警消息中提取工号
        /// </summary>
        /// <param name="alarmMessage">报警消息</param>
        /// <returns>工号，如果提取失败返回空字符串</returns>
        private string ExtractEmployeeId(string alarmMessage)
        {
            try
            {
                if (string.IsNullOrEmpty(alarmMessage))
                {
                    return string.Empty;
                }

                // 提取工号逻辑 - 查找"工号-"后面的文本
                int indexOfId = alarmMessage.IndexOf("工号-");
                if (indexOfId >= 0 && indexOfId + 3 < alarmMessage.Length)
                {
                    string remainingText = alarmMessage.Substring(indexOfId + 3); // 跳过"工号-"
                    // 提取工号部分（到空格、逗号或字符串结束）
                    int endIndex = -1;
                    foreach (char delimiter in new char[] { ' ', ',', '，', '\t', '\n', '\r' })
                    {
                        int delimiterIndex = remainingText.IndexOf(delimiter);
                        if (delimiterIndex >= 0 && (endIndex == -1 || delimiterIndex < endIndex))
                        {
                            endIndex = delimiterIndex;
                        }
                    }

                    string employeeId = endIndex > 0 ? remainingText.Substring(0, endIndex) : remainingText;

                    // 清理工号字符串，移除可能的特殊字符
                    employeeId = employeeId.Trim();

                    // 如果提取失败，尝试使用正则表达式
                    if (string.IsNullOrEmpty(employeeId))
                    {
                        System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(alarmMessage, @"工号-(\w+)");
                        if (match.Success && match.Groups.Count > 1)
                        {
                            employeeId = match.Groups[1].Value;
                        }
                    }

                    return employeeId;
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                if (enableErrorLogging)
                {
                    LogError($"提取工号时出错: {ex.Message}");
                }
                return string.Empty;
            }
        }

        private void label9_Click(object sender, EventArgs e)
        {

        }

        private void textBoxIP_TextChanged(object sender, EventArgs e)
        {

        }

        private void textBoxPort_TextChanged(object sender, EventArgs e)
        {

        }

        private void button1_Click(object sender, EventArgs e)
        {
            
            MessageBox.Show(Constants.CurrentTime);
            int result = string.Compare(Constants.CurrentTime, "234002");

            if (result > 0)
            {
                Console.WriteLine("str1 大于 str2");
            }

        }

        private void listViewAlarmInfo_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 检查是否有选定的项目
            if (listViewAlarmInfo.SelectedItems.Count > 0)
            {
                // 获取选定的项目
                ListViewItem selectedItem = listViewAlarmInfo.SelectedItems[0];

                // 检查项目的子项数量
                if (selectedItem.SubItems.Count > 2) // 确保至少有3列 (时间、设备IP、报警信息)
                {
                    // 获取报警信息列(第3列)的文本数据
                    string alarmMessage = selectedItem.SubItems[2].Text;

                    // 更新文本框内容
                    textBox2.Text = alarmMessage.Replace("报警触发时间：", "").Replace(",", "");
                    
                    // 从报警信息中提取工号并加载照片
                    LoadUserPhoto(alarmMessage);
                }
            }
        }

        private void groupBox3_Enter(object sender, EventArgs e)
        {

        }

        private void button2_Click(object sender, EventArgs e)
        {
            //"报警触发时间：2024/3/26 11:50:04，工号-chenshouzhuang";
            //string TTTT = "报警触发时间：2024/3/26 11:50:04，工号-" + textBox2.Text;//这里传递的时间是不对的
            string TTTT = textBox2.Text;

            // 判断消息是否为空白，如果是空白则不传递
            if (string.IsNullOrWhiteSpace(TTTT))
            {
                MessageBox.Show("消息内容为空，不能传递空白消息！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            string result = TTTT.Replace("报警触发时间：", "").Replace(",", "");
            //string result = Regex.Replace(strAlarmMsg, @"[\u4e00-\u9fa5\p{P}]+", "");
            //MessageBox.Show(result);

            bool messageSent = false;
            
            // 使用文本框中的窗口名称
            string window1Name = textBoxWindow1.Text.Trim();
            string window2Name = textBoxWindow2.Text.Trim();
            
            // 尝试查找第一个窗口
            if (!string.IsNullOrEmpty(window1Name))
            {
                int hWnd = FindWindow(null, window1Name);
                if (hWnd != 0)
                {
                    // 发送消息到第一个窗口
                    messageSent = SendMessageToWindow(hWnd, TTTT, result);
                }
            }
            
            // 如果第一个窗口未找到或发送失败，尝试第二个窗口
            if (!messageSent && !string.IsNullOrEmpty(window2Name))
            {
                int hWnd2 = FindWindow(null, window2Name);
                if (hWnd2 != 0)
                {
                    // 发送消息到第二个窗口
                    messageSent = SendMessageToWindow(hWnd2, TTTT, result);
                }
            }
            
            // 如果消息未成功发送，记录到文件
            if (!messageSent)
            {
                // 未找到消息接受者，设置文本颜色为红色
                textBox2.ForeColor = Color.Red;
                
                // 记录失败消息到文件
                LogFailedMessage(result);
            }
        }

        /// <summary>
        /// 向指定窗口发送消息
        /// </summary>
        /// <param name="hWnd">窗口句柄</param>
        /// <param name="messageText">原始消息文本</param>
        /// <param name="processedResult">处理后的消息文本</param>
        /// <returns>是否发送成功</returns>
        private bool SendMessageToWindow(int hWnd, string messageText, string processedResult)
        {
            byte[] sarr = System.Text.Encoding.Default.GetBytes(messageText);
            int len = sarr.Length;
            COPYDATASTRUCT cds;
            cds.dwData = (IntPtr)Convert.ToInt16(0);  
            cds.cbData = len + 25; //指定lpData内存区域的字节数
            cds.lpData = processedResult; //发送给目标窗口所在进程的数据  
            int sendResult = SendMessage(hWnd, WM_COPYDATA, 0, ref cds);

            // 判断 SendMessage 是否成功
            if (sendResult != 0)
            {
                textBox2.ForeColor = Color.Green; // 设置文本颜色为绿色

                // 找到对应的列表项并设置为绿色
                if (listViewAlarmInfo.SelectedItems.Count > 0)
                {
                    ListViewItem selectedItem = listViewAlarmInfo.SelectedItems[0];
                    selectedItem.ForeColor = Color.Green; // 设置列表项颜色为绿色
                }

                // 从文件中删除成功传递的消息记录
                RemoveMessageFromFile(processedResult);
                return true;
            }
            
            return false;
        }
        
        /// <summary>
        /// 记录失败消息到文件
        /// </summary>
        /// <param name="message">要记录的消息</param>
        private void LogFailedMessage(string message)
        {
            try
            {
                // 如果消息为空则不记录
                if (string.IsNullOrWhiteSpace(message))
                {
                    return;
                }

                // 获取桌面路径
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string datePrefix = DateTime.Now.ToString("yyyyMMdd");
                string filePath = Path.Combine(desktopPath, datePrefix + "传递失败人脸消息.txt");

                // 检查文件是否存在
                bool fileExists = File.Exists(filePath);
                
                // 检查消息是否已存在于文件中
                if (fileExists)
                {
                    // 读取所有行并检查是否已包含此消息
                    string[] existingLines = File.ReadAllLines(filePath);
                    foreach (string line in existingLines)
                    {
                        // 如果消息已存在，则不再写入
                        if (line.Trim() == message.Trim())
                        {
                            return;
                        }
                    }
                }

                // 消息不存在，追加到文件
                File.AppendAllText(filePath, message + Environment.NewLine);
            }
            catch (Exception ex)
            {
                // 记录错误，但不阻塞UI
                LogError("记录失败消息时发生错误：" + ex.Message);
            }
        }

        /// <summary>
        /// 从文件中删除指定消息
        /// </summary>
        /// <param name="messageToRemove">要删除的消息</param>
        private void RemoveMessageFromFile(string messageToRemove)
        {
            try
            {
                // 获取桌面路径和文件名
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string datePrefix = DateTime.Now.ToString("yyyyMMdd");
                string filePath = Path.Combine(desktopPath, datePrefix + "传递失败人脸消息.txt");

                // 检查文件是否存在
                if (!File.Exists(filePath))
                {
                    return; // 文件不存在，直接返回
                }
                
                // 使用文件流处理大文件更高效
                List<string> updatedLines = new List<string>();
                bool fileChanged = false;
                
                using (StreamReader reader = new StreamReader(filePath))
                {
                    string line;
                    while ((line = reader.ReadLine()) != null)
                    {
                        if (line.Trim() != messageToRemove.Trim())
                        {
                            updatedLines.Add(line);
                        }
                        else
                        {
                            fileChanged = true; // 标记文件有变更
                        }
                    }
                }
                
                // 只有在文件内容有变化时才处理
                if (fileChanged)
                {
                    // 检查处理后的内容是否为空
                    if (updatedLines.Count == 0)
                    {
                        // 如果文件内容为空，删除整个文件
                        File.Delete(filePath);
                        LogError("文件已为空，已删除文件: " + filePath);
                    }
                    else
                    {
                        // 文件内容不为空，写回所有行
                        File.WriteAllLines(filePath, updatedLines);
                    }
                }
            }
            catch (Exception ex)
            {
                // 记录错误日志，不阻塞UI
                LogError("删除消息记录时发生错误：" + ex.Message);
            }
        }

        private void textBox2_TextChanged(object sender, EventArgs e)
        {

        }

        private void AlarmDemo_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 使用静态变量避免重复显示确认对话框
            if (isClosing)
            {
                // 已经在关闭流程中，不再处理
                return;
            }

            try
            {
                // 标记为正在关闭
                isClosing = true;
                
                // 如果已经执行过清理，允许直接关闭
                if (alreadyCleaned)
                {
                    return;
                }
                
                // 如果是通过退出按钮关闭的，直接执行清理流程，不再询问
                if (!isExitButtonClicked)
                {
                    // 显示确认对话框
                    DialogResult result = MessageBox.Show("确定要关闭程序吗？", "确认关闭", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    // 用户选择"否"，取消关闭
                    if (result == DialogResult.No)
                    {
                        e.Cancel = true;
                        // 重要：使用计时器延迟重置状态，避免在当前调用堆栈中重置
                        Timer resetTimer = new Timer();
                        resetTimer.Interval = 100;
                        resetTimer.Tick += (s, args) => {
                            isClosing = false;
                            resetTimer.Stop();
                            resetTimer.Dispose();
                        };
                        resetTimer.Start();
                        return;
                    }
                }
                
                // 用户选择"是"或通过退出按钮关闭
                // 标记已清理
                alreadyCleaned = true;
                
                // 执行清理操作
                try
                {
                    // 停止监听
                    if (iListenHandle >= 0)
                    {
                        CHCNetSDK.NET_DVR_StopListen_V30(iListenHandle);
                    }
                    
                    // 注销设备
                    for (int i = 0; i < iDeviceNumber; i++)
                    {
                        if (i < listViewDevice.Items.Count)
                        {
                            try
                            {
                                m_lUserID = Int32.Parse(listViewDevice.Items[i].SubItems[0].Text);
                                CHCNetSDK.NET_DVR_Logout(m_lUserID);
                            }
                            catch
                            {
                                // 忽略可能的错误
                            }
                        }
                    }
                    
                    // 释放SDK资源
                    CHCNetSDK.NET_DVR_Cleanup();
                }
                catch (Exception ex)
                {
                    LogError("释放资源时出错: " + ex.Message);
                }
            }
            finally
            {
                // 确保在此方法结束时恢复状态
                if (e.Cancel)
                {
                    // 如果取消关闭，延时恢复状态
                    Timer resetTimer = new Timer();
                    resetTimer.Interval = 100;
                    resetTimer.Tick += (s, args) => {
                        isClosing = false;
                        resetTimer.Stop();
                        resetTimer.Dispose();
                    };
                    resetTimer.Start();
                }
            }
        }

        // 菜单项点击事件处理程序
        private void SendMessageMenuItem_Click(object sender, EventArgs e)
        {
            // 获取选中的列表项
            if (listViewAlarmInfo.SelectedItems.Count > 0)
            {
                ListViewItem selectedItem = listViewAlarmInfo.SelectedItems[0];
                string strAlarmTime = selectedItem.SubItems[0].Text; // 假设第一个子项是报警时间
                string strDevIP = selectedItem.SubItems[1].Text; // 假设第二个子项是设备IP
                string strAlarmMsg = selectedItem.SubItems[2].Text; // 假设第三个子项是报警信息

                // 模拟点击传递消息按钮的逻辑
                // 这里可以调用您之前的传递消息逻辑
                // 例如，您可以将这些信息传递给 button2_Click 方法
                button2_Click(sender, e); // 直接调用传递消息的逻辑
            }
        }

        /// <summary>
        /// 记录错误到日志文件
        /// </summary>
        /// <param name="errorMessage">错误信息</param>
        private void LogError(string errorMessage)
        {
            // 检查是否启用了错误日志记录
            if (!enableErrorLogging)
            {
                return; // 如果未启用，则直接返回
            }
            
            try
            {
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string datePrefix = DateTime.Now.ToString("yyyyMMdd");
                string logPath = Path.Combine(desktopPath, datePrefix + "_错误日志.txt");
                string logEntry = "[" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss") + "] " + errorMessage + Environment.NewLine;
                File.AppendAllText(logPath, logEntry);
                
                // 在状态栏或界面上显示非阻塞提示（如果有状态栏）
                // 如果界面上有状态栏或错误提示标签，可以在这里更新
                // statusLabel.Text = "发生错误：" + errorMessage;
            }
            catch
            {
                // 即使日志记录失败也不阻塞主流程
            }
        }

        // 添加btnExit_Click方法，解决Designer.cs的引用问题
        private void btnExit_Click(object sender, EventArgs e)
        {
            isExitButtonClicked = true; // 设置标志
            this.Close(); // 关闭窗口
        }

        /// <summary>
        /// 从报警消息中提取工号并加载对应的照片
        /// </summary>
        /// <param name="alarmMsg">报警消息</param>
        private void LoadUserPhoto(string alarmMsg)
        {
            try
            {
                // 从报警消息中提取工号
                string employeeId = "";
                
                // 提取工号逻辑 - 查找"工号-"后面的文本
                int indexOfId = alarmMsg.IndexOf("工号-");
                if (indexOfId >= 0 && indexOfId + 3 < alarmMsg.Length)
                {
                    string remainingText = alarmMsg.Substring(indexOfId + 3); // 跳过"工号-"
                    // 提取工号部分（到空格或字符串结束）
                    int endIndex = remainingText.IndexOf(' ');
                    employeeId = endIndex > 0 ? remainingText.Substring(0, endIndex) : remainingText;
                    
                    // 如果提取失败，尝试使用正则表达式
                    if (string.IsNullOrEmpty(employeeId))
                    {
                        System.Text.RegularExpressions.Match match = System.Text.RegularExpressions.Regex.Match(alarmMsg, @"工号-(\w+)");
                        if (match.Success && match.Groups.Count > 1)
                        {
                            employeeId = match.Groups[1].Value;
                        }
                    }
                }
                
                // 如果成功提取到工号
                if (!string.IsNullOrEmpty(employeeId))
                {
                    // 确定照片路径，纯数字为老师，其他为学生
                    string photoFolder = System.Text.RegularExpressions.Regex.IsMatch(employeeId, @"^\d+$") 
                        ? "老师照片" 
                        : "学生照片";
                    
                    // 构建可能的照片文件路径（支持jpg、bmp和png格式）
                    string basePath = Path.Combine(Application.StartupPath, photoFolder, employeeId);
                    string jpgPath = basePath + ".jpg";
                    string bmpPath = basePath + ".bmp";
                    string pngPath = basePath + ".png";
                    
                    bool photoLoaded = false;
                    string newPhotoPath = string.Empty;
                    
                    // 检查找到的照片路径
                    if (File.Exists(jpgPath))
                    {
                        newPhotoPath = jpgPath;
                    }
                    else if (File.Exists(bmpPath))
                    {
                        newPhotoPath = bmpPath;
                    }
                    else if (File.Exists(pngPath))
                    {
                        newPhotoPath = pngPath;
                    }
                    
                    // 检查是否与当前已加载的照片相同
                    if (!string.IsNullOrEmpty(newPhotoPath))
                    {
                        // 只有当照片路径不同时才重新加载
                        if (!string.Equals(newPhotoPath, currentPhotoPath, StringComparison.OrdinalIgnoreCase))
                        {
                            // 加载新照片
                            using (FileStream fs = new FileStream(newPhotoPath, FileMode.Open, FileAccess.Read))
                            {
                                picUserPhoto.Image = Image.FromStream(fs);
                                photoLoaded = true;
                                currentPhotoPath = newPhotoPath; // 保存当前照片路径
                            }
                        }
                        else
                        {
                            // 同一张照片，无需重新加载
                            photoLoaded = true;
                        }
                    }
                    
                    // 如果没有找到照片文件
                    if (!photoLoaded)
                    {
                        // 照片不存在，显示默认图片或清空
                        picUserPhoto.Image = null;
                        currentPhotoPath = string.Empty; // 清空照片路径
                        
                        if (enableErrorLogging)
                        {
                            LogError("未找到工号 " + employeeId + " 的照片文件，查找路径: " + basePath);
                        }
                    }
                    
                    // 确保只在报警布防标签页激活时显示照片
                    picUserPhoto.Visible = mainTabControl.SelectedIndex == 0;
                }
                else if (enableErrorLogging)
                {
                    LogError("无法从报警消息中提取工号: " + alarmMsg);
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不影响主程序运行
                if (enableErrorLogging)
                {
                    LogError("加载照片时出错: " + ex.Message);
                }
                picUserPhoto.Image = null;
            }
        }

        private void mainTabControl_SelectedIndexChanged(object sender, EventArgs e)
        {
            // 根据当前选中的标签页设置照片控件的可见性
            if (mainTabControl.SelectedIndex == 0) // 报警布防标签页
            {
                picUserPhoto.Visible = true; // 显示照片
            }
            else
            {
                picUserPhoto.Visible = false; // 隐藏照片
            }
        }

        /// <summary>
        /// 发送布防成功消息
        /// </summary>
        /// <param name="userId">用户ID</param>
        private void SendAlarmSuccessMessage(Int32 userId)
        {
            //布防成功之后传递消息
            string currentTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss");
            string TTTT = "报警触发时间：" + currentTime + "，工号-chenggong" + userId;
            string result = TTTT.Replace("报警触发时间：", "").Replace(",", "");
            
            // 使用文本框中的窗口名称
            string window1Name = textBoxWindow1.Text.Trim();
            string window2Name = textBoxWindow2.Text.Trim();
            
            bool messageSent = false;
            
            // 尝试发送到第一个窗口
            if (!string.IsNullOrEmpty(window1Name))
            {
                int hWnd = FindWindow(null, window1Name);
                if (hWnd != 0)
                {
                    byte[] sarr = System.Text.Encoding.Default.GetBytes(TTTT);
                    int len = sarr.Length;
                    COPYDATASTRUCT cds;
                    cds.dwData = (IntPtr)Convert.ToInt16(0);  
                    cds.cbData = len + 25; //指定lpData内存区域的字节数
                    cds.lpData = result; //发送给目标窗口所在进程的数据  
                    int sendResult = SendMessage(hWnd, WM_COPYDATA, 0, ref cds);
                    
                    if (sendResult != 0)
                    {
                        messageSent = true;
                    }
                }
            }
            
            // 如果第一个窗口未找到或发送失败，尝试第二个窗口
            if (!messageSent && !string.IsNullOrEmpty(window2Name))
            {
                int hWnd2 = FindWindow(null, window2Name);
                if (hWnd2 != 0)
                {
                    byte[] sarr = System.Text.Encoding.Default.GetBytes(TTTT);
                    int len = sarr.Length;
                    COPYDATASTRUCT cds;
                    cds.dwData = (IntPtr)Convert.ToInt16(0);  
                    cds.cbData = len + 25; //指定lpData内存区域的字节数
                    cds.lpData = result; //发送给目标窗口所在进程的数据  
                    int sendResult = SendMessage(hWnd2, WM_COPYDATA, 0, ref cds);
                    
                    if (sendResult != 0)
                    {
                        messageSent = true;
                    }
                }
            }
            
            // 如果两个窗口都未找到或发送失败
            if (!messageSent)
            {
                LogError("布防成功后未找到消息接收窗口");
            }
        }

        /// <summary>
        /// 照片控件点击事件处理
        /// </summary>
        private void picUserPhoto_Click(object sender, EventArgs e)
        {
            try
            {
                if (picUserPhoto.Image == null)
                {
                    return;
                }

                // 直接使用原始照片路径
                if (!string.IsNullOrEmpty(currentPhotoPath) && File.Exists(currentPhotoPath))
                {
                    // 使用系统默认应用打开原始照片文件
                    System.Diagnostics.Process.Start(currentPhotoPath);
                }
                else
                {
                    // 如果没有原始路径
                    if (enableErrorLogging)
                    {
                        LogError("无法找到原始照片文件");
                    }
                    else
                    {
                        MessageBox.Show("无法找到原始照片文件", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                if (enableErrorLogging)
                {
                    LogError("打开照片时出错: " + ex.Message);
                }
                else
                {
                    MessageBox.Show("打开照片时出错: " + ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

    }
}

namespace CopyDataStruct
{
    public struct COPYDATASTRUCT
    {
        public IntPtr dwData;
        public int cbData;
        [MarshalAs(UnmanagedType.LPStr)]
        public string lpData;
    }
}