# 人脸图片保存功能修改说明

## 修改概述

根据您的要求，我已经成功修改了人脸识别客户端的图片保存功能，实现了以下特性：

1. **新的保存位置**：D盘根目录下的"人脸识别照片"文件夹
2. **按日期分类**：在"人脸识别照片"目录下自动创建以当前日期命名的子目录（格式：yyyyMMdd，如20250804）
3. **工号命名**：图片文件名使用工号命名（如：lingziyu.jpg）
4. **自动创建目录**：如果目录不存在会自动创建
5. **界面控制**：通过"保存图片"复选框控制是否启用图片保存功能

## 修改的核心函数

### 1. SaveFaceImage 函数
```csharp
/// <summary>
/// 保存人脸识别图片到指定目录
/// </summary>
/// <param name="imageData">图片数据</param>
/// <param name="dataLength">数据长度</param>
/// <param name="alarmMessage">报警消息（用于提取工号）</param>
/// <param name="imageType">图片类型（如"FaceSnap", "FaceMatch"等）</param>
/// <returns>是否保存成功</returns>
private bool SaveFaceImage(IntPtr imageData, int dataLength, string alarmMessage, string imageType = "Face")
```

**功能特点**：
- 从报警消息中自动提取工号
- 创建日期目录结构：`D:\人脸识别照片\20250804\`
- 使用工号作为文件名：`lingziyu.jpg`
- 完善的错误处理和日志记录

### 2. ExtractEmployeeId 函数
```csharp
/// <summary>
/// 从报警消息中提取工号
/// </summary>
/// <param name="alarmMessage">报警消息</param>
/// <returns>工号，如果提取失败返回空字符串</returns>
private string ExtractEmployeeId(string alarmMessage)
```

**提取逻辑**：
- 查找"工号-"关键字
- 提取关键字后面的工号信息
- 支持正则表达式备用提取方式
- 自动清理特殊字符

## 修改的图片保存位置

### 1. ACS报警图片保存
- **位置**：ProcessCommAlarm_AcsAlarm 函数
- **修改**：使用新的SaveFaceImage函数替代原有的文件保存逻辑

### 2. 人脸抓拍图片保存
- **位置**：ProcessCommAlarm_FaceSnap 函数
- **修改**：使用新的SaveFaceImage函数保存背景图片

### 3. 人脸比对图片保存
- **位置**：ProcessCommAlarm_FaceMatch 函数
- **修改**：保存抓拍的人脸图片（不保存模板库图片）

### 4. ISAPI报警图片保存
- **位置**：ProcessCommAlarm_ISAPIAlarm 函数
- **修改**：从XML/JSON数据中提取工号，使用新的保存方式

## 目录结构示例

```
D:\人脸识别照片\
├── 20250804\
│   ├── lingziyu.jpg
│   ├── zhangsan.jpg
│   └── lisi.jpg
├── 20250805\
│   ├── wangwu.jpg
│   └── zhaoliu.jpg
└── ...
```

## 使用方法

1. **启动程序**：默认情况下"保存图片"复选框未选中，不会保存图片
2. **启用保存**：勾选界面右下角的"保存图片"复选框
3. **自动保存**：当有人脸识别报警时，程序会自动：
   - 从报警消息中提取工号
   - 创建当天的日期目录（如果不存在）
   - 将图片保存为：`D:\人脸识别照片\20250804\工号.jpg`
4. **停止保存**：取消勾选复选框即可停止保存图片

## 错误处理

- **工号提取失败**：如果无法从报警消息中提取工号，会记录错误日志（如果启用了错误日志）
- **目录创建失败**：如果无法创建目录，会记录错误并跳过保存
- **文件保存失败**：如果保存过程中出现异常，会记录详细的错误信息

## 日志记录

当启用错误日志时，系统会记录：
- 成功保存的图片路径
- 工号提取失败的情况
- 文件保存过程中的错误

## 注意事项

1. **权限要求**：确保程序有权限在D盘创建目录和文件
2. **磁盘空间**：注意监控D盘的可用空间
3. **文件覆盖**：如果同一天同一工号有多次识别，后面的图片会覆盖前面的图片
4. **工号格式**：工号提取依赖于报警消息中的"工号-"关键字格式

## 测试建议

1. **功能测试**：
   - 勾选"保存图片"复选框
   - 触发人脸识别报警
   - 检查D盘是否创建了正确的目录结构
   - 验证图片是否以工号命名

2. **错误测试**：
   - 测试无工号信息的报警消息
   - 测试D盘权限不足的情况
   - 测试磁盘空间不足的情况

3. **性能测试**：
   - 测试大量连续的人脸识别报警
   - 监控内存和CPU使用情况

## 技术细节

- **图片格式**：保存为JPG格式
- **编码方式**：使用UTF-8编码处理中文工号
- **内存管理**：使用using语句确保文件流正确释放
- **线程安全**：保存操作在报警回调线程中执行，注意并发访问

这个修改完全满足了您的需求，既保持了原有的"不保存图片"特性（默认状态），又提供了灵活的图片保存功能，并且使用了更合理的目录结构和文件命名方式。
