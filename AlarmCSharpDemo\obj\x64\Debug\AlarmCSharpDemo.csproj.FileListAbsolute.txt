C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe.config
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csprojResolveAssemblyReference.cache
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.AlarmDemo.resources
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.Properties.Resources.resources
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csproj.GenerateResource.Cache
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\CH-HCNetSDKV6.1.9.48_build20230410_win64\Demo示例\3- C# 开发示例\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.pdb
C:\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe.config
C:\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe
C:\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.pdb
C:\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.AlarmDemo.resources
C:\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.Properties.Resources.resources
C:\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csproj.GenerateResource.Cache
C:\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.exe
C:\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe.config
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\bin\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.AlarmDemo.resources
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.Properties.Resources.resources
C:\Users\<USER>\Desktop\4-报警布防监听\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csproj.GenerateResource.Cache
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe.config
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\bin\AlarmCSharpDemo.pdb
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.AlarmDemo.resources
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.Properties.Resources.resources
C:\Users\<USER>\Desktop\4-报警布防监听不保存图片\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csproj.GenerateResource.Cache
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe.config
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.exe
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.pdb
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\bin\AlarmCSharpDemo.exe
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\bin\AlarmCSharpDemo.pdb
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.AlarmDemo.resources
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.Properties.Resources.resources
E:\Cursor\人脸识别客户端\4-报警布防监听不保存图片-两个设备2025\AlarmCSharpDemo\obj\x64\Debug\AlarmCSharpDemo.csproj.GenerateResource.Cache
